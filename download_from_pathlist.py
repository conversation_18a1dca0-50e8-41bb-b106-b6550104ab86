#!/usr/bin/env python3
"""
基于现有路径列表下载cam_front_120图片的脚本
从local_z10_100.json中读取路径列表，提取并下载所有JPG文件
"""

import json
import os
import sys
import subprocess
from pathlib import Path
from urllib.parse import urlparse
import concurrent.futures
from functools import partial
from tqdm import tqdm

def create_directory_structure(s3_path, base_dir="jpg"):
    """
    根据S3路径创建本地目录结构
    例如: s3://bucket/path/to/file.jpg -> jpg/bucket/path/to/file.jpg
    """
    # 移除s3://前缀
    if s3_path.startswith("s3://"):
        path_without_prefix = s3_path[5:]  # 移除"s3://"
    else:
        path_without_prefix = s3_path

    # 组合本地路径
    local_path = os.path.join(base_dir, path_without_prefix)

    # 创建目录
    os.makedirs(os.path.dirname(local_path), exist_ok=True)

    return local_path

def extract_jpg_paths_from_json(json_file_path):
    """
    从JSON文件中提取所有sensor_data.cam_front_120.file_path
    """
    jpg_paths = []

    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"正在处理JSON文件: {json_file_path}")

        if 'frames' not in data:
            print(f"警告: {json_file_path} 中没有frames字段")
            return jpg_paths

        # 提取所有帧的cam_front_120.file_path
        for frame_idx, frame in enumerate(data['frames']):
            if 'sensor_data' not in frame:
                continue

            sensor_data = frame['sensor_data']
            if 'cam_front_120' not in sensor_data:
                continue

            cam_front_120 = sensor_data['cam_front_120']
            if 'file_path' not in cam_front_120:
                continue

            jpg_path = cam_front_120['file_path']
            if not jpg_path.endswith('.jpg'):
                continue

            jpg_paths.append(jpg_path)

        print(f"从 {json_file_path} 中提取到 {len(jpg_paths)} 个JPG文件路径")

    except json.JSONDecodeError as e:
        print(f"JSON解析错误 {json_file_path}: {e}")
    except Exception as e:
        print(f"处理文件错误 {json_file_path}: {e}")

    return jpg_paths

def download_jpg_with_aws(s3_path, local_path, max_retries=3):
    """
    使用aws s3命令下载单个JPG文件
    """
    if os.path.exists(local_path):
        print(f"文件已存在，跳过: {local_path}")
        return True

    for attempt in range(max_retries):
        try:
            # 构建aws s3 cp命令
            cmd = ['aws', 's3', 'cp', s3_path, local_path]
            print(f"下载: {s3_path} -> {local_path}")

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                print(f"下载成功: {local_path}")
                return True
            else:
                print(f"下载失败 (尝试 {attempt + 1}/{max_retries}): {result.stderr}")

        except subprocess.TimeoutExpired:
            print(f"下载超时 (尝试 {attempt + 1}/{max_retries}): {s3_path}")
        except Exception as e:
            print(f"下载错误 (尝试 {attempt + 1}/{max_retries}): {e}")

    print(f"下载失败，已达到最大重试次数: {s3_path}")
    return False

def process_path_list(pathlist_file, base_dir="jpg", max_workers=5, limit=None):
    """
    处理路径列表文件，下载所有JPG文件
    """
    try:
        with open(pathlist_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if 'paths' not in data:
            print(f"错误: {pathlist_file} 中没有paths字段")
            return 0, 0

        json_files = data['paths']
        print(f"找到 {len(json_files)} 个JSON文件路径")

        # 限制处理数量（用于测试）
        if limit:
            json_files = json_files[:limit]
            print(f"限制处理前 {limit} 个文件")

        all_jpg_paths = []

        # 从每个JSON文件中提取JPG路径
        for idx, json_file in enumerate(tqdm(json_files, desc="处理JSON文件", unit="文件")):
            jpg_paths = extract_jpg_paths_from_json(json_file)
            all_jpg_paths.extend(jpg_paths)

        print(f"\n总共提取到 {len(all_jpg_paths)} 个JPG文件路径")

        if not all_jpg_paths:
            print("没有找到任何JPG文件路径")
            return 0, 0

        # 创建本地路径列表
        download_tasks = []
        for jpg_path in all_jpg_paths:
            local_path = create_directory_structure(jpg_path, base_dir)
            download_tasks.append((jpg_path, local_path))

        print(f"准备下载 {len(download_tasks)} 个文件到 {base_dir} 目录")

        # 并发下载JPG文件
        downloaded_count = 0
        failed_count = 0

        download_func = partial(download_jpg_with_aws)

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_path = {
                executor.submit(download_func, s3_path, local_path): (s3_path, local_path)
                for s3_path, local_path in download_tasks
            }

            # 使用tqdm显示下载进度
            with tqdm(total=len(download_tasks), desc="下载文件", unit="文件") as pbar:
                for future in concurrent.futures.as_completed(future_to_path):
                    s3_path, local_path = future_to_path[future]
                    try:
                        success = future.result()
                        if success:
                            downloaded_count += 1
                        else:
                            failed_count += 1
                    except Exception as e:
                        print(f"下载异常: {s3_path}, 错误: {e}")
                        failed_count += 1
                    finally:
                        pbar.update(1)

        return downloaded_count, failed_count

    except FileNotFoundError:
        print(f"错误: 找不到路径列表文件: {pathlist_file}")
        return 0, 0
    except json.JSONDecodeError as e:
        print(f"JSON解析错误 {pathlist_file}: {e}")
        return 0, 0
    except Exception as e:
        print(f"处理路径列表错误: {e}")
        return 0, 0

def main():
    import argparse

    parser = argparse.ArgumentParser(description='基于路径列表下载cam_front_120图片')
    parser.add_argument('--pathlist', default='local_z10_100.json',
                       help='路径列表文件 (默认: local_z10_100.json)')
    parser.add_argument('--output-dir', default='jpg', help='输出目录 (默认: jpg)')
    parser.add_argument('--max-workers', type=int, default=5, help='并发下载数量 (默认: 5)')
    parser.add_argument('--limit', type=int, help='限制处理的JSON文件数量 (用于测试)')
    parser.add_argument('--dry-run', action='store_true', help='只提取路径，不下载文件')

    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 如果只是dry run，只提取路径并显示统计信息
    if args.dry_run:
        print("=== DRY RUN MODE - 只提取路径，不下载文件 ===")

        try:
            with open(args.pathlist, 'r', encoding='utf-8') as f:
                data = json.load(f)

            json_files = data['paths'] if 'paths' in data else []

            if args.limit:
                json_files = json_files[:args.limit]

            all_jpg_paths = []

            for idx, json_file in enumerate(tqdm(json_files, desc="DRY RUN - 处理JSON文件", unit="文件")):
                jpg_paths = extract_jpg_paths_from_json(json_file)
                all_jpg_paths.extend(jpg_paths)

            print(f"\n=== DRY RUN 结果 ===")
            print(f"处理的JSON文件数量: {len(json_files)}")
            print(f"提取的JPG文件数量: {len(all_jpg_paths)}")
            print(f"输出目录: {args.output_dir}")

            if all_jpg_paths:
                print(f"\n前5个JPG路径示例:")
                for i, path in enumerate(all_jpg_paths[:5]):
                    print(f"  {i+1}: {path}")

        except Exception as e:
            print(f"DRY RUN 失败: {e}")

        return

    # 正常下载模式
    print("=== 开始下载JPG文件 ===")
    downloaded, failed = process_path_list(
        args.pathlist,
        base_dir=args.output_dir,
        max_workers=args.max_workers,
        limit=args.limit
    )

    print(f"\n=== 任务完成 ===")
    print(f"总下载成功: {downloaded}")
    print(f"总下载失败: {failed}")
    print(f"输出目录: {args.output_dir}")

if __name__ == "__main__":
    main()