#!/usr/bin/env python3
import json
import os
import random
import time
import cv2
import numpy as np
from typing import List, Tu<PERSON>, Dict

class FrameData:
    def __init__(self, clip_path: str, frame_index: int, video_path: str, jpg_path: str, key_frame_index: int):
        self.clip_path = clip_path
        self.frame_index = frame_index
        self.video_path = video_path
        self.jpg_path = jpg_path
        self.key_frame_index = key_frame_index

def load_frame_data():
    """加载所有帧数据"""
    print("加载clip数据...")
    frame_data_list = []

    with open('local_z10_10.json', 'r') as f:
        paths_data = json.load(f)

    clip_paths = paths_data['paths']

    for clip_path in clip_paths:
        # 读取对应的video json文件
        clip_dir = os.path.dirname(clip_path)
        clip_name = os.path.splitext(os.path.basename(clip_path))[0]
        video_json_path = os.path.join(clip_dir, f"{clip_name}_video.json")

        if not os.path.exists(video_json_path):
            print(f"警告: 找不到video json文件: {video_json_path}")
            continue

        try:
            with open(video_json_path, 'r') as f:
                video_data = json.load(f)

            video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

            for frame in video_data.get('frames', []):
                sensor_data = frame.get('sensor_data', {})
                cam_data = sensor_data.get('cam_front_120', {})

                if 'video_path' in cam_data and 'file_path' in cam_data:
                    frame_data = FrameData(
                        clip_path=clip_path,
                        frame_index=cam_data.get('frame_index', 0),
                        video_path=cam_data['video_path'],
                        jpg_path=cam_data['file_path'],
                        key_frame_index=cam_data.get('key_frame_index', 0)
                    )
                    frame_data_list.append(frame_data)

        except Exception as e:
            print(f"加载clip数据失败: {e}")
            continue

    print(f"总共加载了 {len(frame_data_list)} 帧数据")
    return frame_data_list

def read_jpg_frame(jpg_path: str) -> np.ndarray:
    """从JPG文件读取图片"""
    if not os.path.exists(jpg_path):
        raise Exception(f"JPG文件不存在: {jpg_path}")
    return cv2.imread(jpg_path)

def read_video_frame(video_path: str, frame_index: int) -> np.ndarray:
    """从视频读取指定帧"""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise Exception(f"无法打开视频文件: {video_path}")

    try:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
        ret, frame = cap.read()
        if not ret:
            raise Exception(f"无法读取视频帧: {video_path} 第{frame_index}帧")
        return frame
    finally:
        cap.release()

def experiment2_continuous_frames(group_size: int = 5, total_groups: int = 20):
    """实验2: 5帧一组连续帧读取"""
    print(f"\n=== 实验2: {group_size}帧一组连续帧读取 ({total_groups}组) ===")

    frame_data_list = load_frame_data()
    if len(frame_data_list) == 0:
        print("没有可用的帧数据")
        return

    # 按clip分组
    frames_by_clip = {}
    for frame_data in frame_data_list:
        clip_key = frame_data.clip_path
        if clip_key not in frames_by_clip:
            frames_by_clip[clip_key] = []
        frames_by_clip[clip_key].append(frame_data)

    # 为每个clip按frame_index排序
    for clip_key in frames_by_clip:
        frames_by_clip[clip_key].sort(key=lambda x: x.frame_index)

    # 随机选择组和起始帧
    all_groups = []
    for clip_key, frames in frames_by_clip.items():
        if len(frames) >= group_size:
            # 计算可能的起始位置
            max_start = len(frames) - group_size
            for start_pos in range(max_start + 1):
                group = frames[start_pos:start_pos + group_size]
                all_groups.append((clip_key, start_pos, group))

    # 随机选择total_groups个组
    selected_groups = random.sample(all_groups, min(total_groups, len(all_groups)))
    selected_frames = []

    for clip_key, start_pos, group in selected_groups:
        selected_frames.extend(group)

    print(f"选择了 {len(selected_groups)} 个连续帧组，共 {len(selected_frames)} 帧")

    # JPG读取测试
    print("\n🖼️  JPG读取测试...")
    start_time = time.time()
    jpg_results = []
    jpg_errors = 0
    for i, frame_data in enumerate(selected_frames):
        try:
            frame = read_jpg_frame(frame_data.jpg_path)
            jpg_results.append(frame.shape)
            if (i + 1) % 25 == 0:
                print(f"  已读取 {i + 1}/{len(selected_frames)} 帧")
        except Exception as e:
            jpg_errors += 1
            print(f"  JPG读取错误 (帧{i+1}): {e}")
    jpg_time = time.time() - start_time

    print(f"JPG读取完成: {jpg_time:.4f}秒, 成功 {len(jpg_results)} 帧, 失败 {jpg_errors} 帧")

    # 视频读取测试
    print("\n🎬 视频读取测试...")
    start_time = time.time()
    video_results = []
    video_errors = 0
    for i, frame_data in enumerate(selected_frames):
        try:
            frame = read_video_frame(frame_data.video_path, frame_data.frame_index)
            video_results.append(frame.shape)
            if (i + 1) % 25 == 0:
                print(f"  已读取 {i + 1}/{len(selected_frames)} 帧")
        except Exception as e:
            video_errors += 1
            print(f"  视频读取错误 (帧{i+1}): {e}")
    video_time = time.time() - start_time

    print(f"视频读取完成: {video_time:.4f}秒, 成功 {len(video_results)} 帧, 失败 {video_errors} 帧")

    # 结果分析
    print(f"\n📊 实验2结果:")
    print(f"  组数: {len(selected_groups)}")
    print(f"  总帧数: {len(selected_frames)}")
    print(f"  JPG时间: {jpg_time:.4f}秒")
    print(f"  视频时间: {video_time:.4f}秒")
    print(f"  JPG平均每帧: {(jpg_time/len(selected_frames))*1000:.2f}ms")
    print(f"  视频平均每帧: {(video_time/len(selected_frames))*1000:.2f}ms")

    speedup = jpg_time / video_time if video_time > 0 else 0
    if speedup > 1:
        print(f"  🚀 视频比JPG快 {speedup:.2f} 倍")
    else:
        print(f"  🐌 JPG比视频快 {1/speedup:.2f} 倍")

    # 保存结果
    result = {
        'experiment': 'experiment2_continuous_frames',
        'groups_count': len(selected_groups),
        'frame_count': len(selected_frames),
        'jpg_time': jpg_time,
        'video_time': video_time,
        'jpg_errors': jpg_errors,
        'video_errors': video_errors,
        'jpg_avg_ms': (jpg_time/len(selected_frames))*1000,
        'video_avg_ms': (video_time/len(selected_frames))*1000,
        'speedup': speedup
    }

    with open('experiment2_result.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    print(f"\n结果已保存到: experiment2_result.json")
    return result

if __name__ == "__main__":
    result = experiment2_continuous_frames(5, 20)