#!/usr/bin/env python3
import json
import os
import glob
from pathlib import Path

def get_file_size(file_path):
    """获取文件大小（字节）"""
    try:
        return os.path.getsize(file_path)
    except OSError:
        return 0

def format_size(size_bytes):
    """格式化文件大小为可读格式"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def clarify_jpg_statistics():
    """澄清JPG统计的不同维度"""
    print("=== JPG统计维度澄清 ===")

    # 1. 统计这10个clip中实际包含的JPG文件
    print("\n1. 这10个clip中的JPG文件统计:")
    try:
        with open('local_z10_10.json', 'r') as f:
            paths_data = json.load(f)

        clip_paths = paths_data['paths']
        clip_jpg_files = set()
        total_clip_jpg_size = 0
        total_frames = 0

        for i, clip_path in enumerate(clip_paths):
            try:
                with open(clip_path, 'r') as f:
                    clip_data = json.load(f)

                clip_jpg_count = 0
                clip_jpg_size = 0

                for frame in clip_data.get('frames', []):
                    sensor_data = frame.get('sensor_data', {})
                    cam_data = sensor_data.get('cam_front_120', {})

                    if 'file_path' in cam_data:
                        original_path = cam_data['file_path']
                        # 转换s3路径为本地路径
                        if original_path.startswith("s3://"):
                            local_path = f"/data/workspace/vframe/jpg/{original_path[5:]}"
                        else:
                            local_path = original_path

                        clip_jpg_files.add(local_path)
                        jpg_size = get_file_size(local_path)
                        total_clip_jpg_size += jpg_size
                        clip_jpg_size += jpg_size
                        clip_jpg_count += 1

                total_frames += clip_jpg_count
                print(f"  Clip {i+1} ({os.path.basename(clip_path)}): {clip_jpg_count} 帧, {format_size(clip_jpg_size)}")

            except Exception as e:
                print(f"  Clip {i+1} 处理失败: {e}")

        print(f"\n  这10个clip总计:")
        print(f"    去重后的JPG文件数: {len(clip_jpg_files)}")
        print(f"    总帧数（含重复）: {total_frames}")
        print(f"    总存储大小: {format_size(total_clip_jpg_size)}")
        print(f"    平均每张JPG: {format_size(total_clip_jpg_size/len(clip_jpg_files))}")

    except Exception as e:
        print(f"  分析失败: {e}")

    # 2. 统计jpg目录下所有JPG文件
    print("\n2. jpg目录下所有JPG文件:")
    all_jpg_files = []
    for pattern in [
        "jpg/**/*.jpg",
        "jpg/**/*.jpeg"
    ]:
        all_jpg_files.extend(glob.glob(pattern, recursive=True))

    all_jpg_files = list(set(all_jpg_files))  # 去重
    total_all_jpg_size = sum(get_file_size(f) for f in all_jpg_files)

    print(f"    所有JPG文件数: {len(all_jpg_files)}")
    print(f"    总存储大小: {format_size(total_all_jpg_size)}")
    print(f"    平均每张JPG: {format_size(total_all_jpg_size/len(all_jpg_files))}")

    # 3. 对比分析
    print("\n3. 对比分析:")
    clip_jpg_count = len(clip_jpg_files)
    other_jpg_count = len(all_jpg_files) - clip_jpg_count

    print(f"    这10个clip相关JPG: {clip_jpg_count} 张")
    print(f"    其他JPG文件: {other_jpg_count} 张")
    print(f"    这10个clip占比: {(clip_jpg_count/len(all_jpg_files))*100:.1f}%")

    if total_all_jpg_size > 0:
        print(f"    这10个clip存储占比: {(total_clip_jpg_size/total_all_jpg_size)*100:.1f}%")

    # 4. 按目录分布分析
    print("\n4. JPG文件目录分布:")
    dir_stats = {}
    for jpg_file in all_jpg_files:
        dir_name = os.path.dirname(jpg_file)
        dir_name = dir_name.replace('/data/workspace/vframe/jpg/', '')

        # 只显示前两级目录
        parts = dir_name.split('/')
        if len(parts) > 2:
            dir_name = '/'.join(parts[:2]) + '/...'

        if dir_name not in dir_stats:
            dir_stats[dir_name] = {'count': 0, 'size': 0}
        dir_stats[dir_name]['count'] += 1
        dir_stats[dir_name]['size'] += get_file_size(jpg_file)

    # 按文件数量排序，显示前10个目录
    sorted_dirs = sorted(dir_stats.items(), key=lambda x: x[1]['count'], reverse=True)
    print("    前10个目录:")
    for dir_name, stats in sorted_dirs[:10]:
        print(f"      {dir_name}: {stats['count']} 张, {format_size(stats['size'])}")

def main():
    print("JPG统计维度详细分析")
    print("=" * 60)
    clarify_jpg_statistics()

if __name__ == "__main__":
    main()