#!/usr/bin/env python3
import json
import os
import subprocess
import time
from pathlib import Path

def s3_to_local_path(s3_path):
    """将s3://路径转换为本地路径"""
    if s3_path.startswith("s3://"):
        # 移除s3://前缀
        path_without_prefix = s3_path[5:]
        # 构建本地路径：/data/workspace/vframe/jpg/bucket/relative_path
        return f"/data/workspace/vframe/jpg/{path_without_prefix}"
    return s3_path

def extract_frames_from_clip(clip_path):
    """从clip json中提取帧信息"""
    with open(clip_path, 'r') as f:
        clip_data = json.load(f)

    frames_info = []
    for i, frame in enumerate(clip_data.get('frames', [])):
        sensor_data = frame.get('sensor_data', {})
        cam_data = sensor_data.get('cam_front_120', {})

        if 'file_path' in cam_data:
            original_path = cam_data['file_path']
            local_path = s3_to_local_path(original_path)

            frames_info.append({
                'frame_index': i,
                'original_path': original_path,
                'local_path': local_path,
                'sensor_data': cam_data
            })

    return frames_info, clip_data

def create_video_from_frames(frames_info, output_video_path):
    """从图片序列创建H265视频"""
    if not frames_info:
        print("没有找到有效的图片帧")
        return False

    # 创建临时文件夹存储图片链接
    temp_dir = "/tmp/video_frames_" + str(int(time.time()))
    os.makedirs(temp_dir, exist_ok=True)

    try:
        # 创建符号链接或复制图片到临时目录
        frame_files = []
        for i, frame_info in enumerate(frames_info):
            local_path = frame_info['local_path']

            if os.path.exists(local_path):
                # 创建符号链接
                link_path = os.path.join(temp_dir, f"frame_{i:06d}.jpg")
                os.symlink(local_path, link_path)
                frame_files.append(link_path)
            else:
                print(f"警告: 图片文件不存在: {local_path}")

        if not frame_files:
            print("没有找到任何有效的图片文件")
            return False

        # 使用ffmpeg创建H265视频
        cmd = [
            'ffmpeg', '-y',  # 覆盖输出文件
            '-framerate', '30',  # 帧率
            '-i', os.path.join(temp_dir, 'frame_%06d.jpg'),  # 输入图片序列
            '-c:v', 'libx265',  # 使用H265编码
            '-x265-params', 'keyint=10:min-keyint=10:no-scenecut=1',  # 关键帧间隔10
            '-crf', '20',  # CRF 20
            '-pix_fmt', 'yuv420p',
            output_video_path
        ]

        print(f"开始生成视频: {output_video_path}")
        print(f"使用 {len(frame_files)} 帧图片")

        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        end_time = time.time()

        if result.returncode == 0:
            print(f"视频生成成功: {output_video_path}")
            print(f"耗时: {end_time - start_time:.2f} 秒")
            return True
        else:
            print(f"视频生成失败: {result.stderr}")
            return False

    except Exception as e:
        print(f"创建视频时出错: {str(e)}")
        return False
    finally:
        # 清理临时目录
        try:
            import shutil
            shutil.rmtree(temp_dir)
        except:
            pass

def create_video_json(original_clip_data, frames_info, video_path, output_json_path):
    """创建包含视频信息的新JSON文件"""
    # 复制原始数据
    video_clip_data = original_clip_data.copy()

    # 计算关键帧信息
    key_frame_indices = []
    for i in range(len(frames_info)):
        if i % 10 == 0:  # 每10帧一个关键帧
            key_frame_indices.append(i)

    # 更新frames中的sensor_data
    for i, frame in enumerate(video_clip_data.get('frames', [])):
        if i < len(frames_info):
            sensor_data = frame.get('sensor_data', {})
            cam_data = sensor_data.get('cam_front_120', {})

            # 更新file_path为本地路径
            cam_data['file_path'] = frames_info[i]['local_path']

            # 添加新的字段
            cam_data['video_path'] = video_path
            cam_data['frame_index'] = i
            cam_data['key_frame_index'] = key_frame_indices[min(i // 10, len(key_frame_indices) - 1)] if key_frame_indices else 0

            # 更新sensor_data
            sensor_data['cam_front_120'] = cam_data
            frame['sensor_data'] = sensor_data

    # 保存新的JSON文件
    with open(output_json_path, 'w') as f:
        json.dump(video_clip_data, f, indent=2, ensure_ascii=False)

    print(f"已创建视频JSON文件: {output_json_path}")

def main():
    # 读取paths列表
    with open('local_z10_10.json', 'r') as f:
        paths_data = json.load(f)

    paths = paths_data['paths']

    total_start_time = time.time()

    for i, clip_path in enumerate(paths):
        print(f"\n处理第 {i+1}/{len(paths)} 个clip: {clip_path}")

        try:
            # 提取帧信息
            frames_info, original_clip_data = extract_frames_from_clip(clip_path)

            if not frames_info:
                print(f"警告: clip {clip_path} 中没有找到有效的帧信息")
                continue

            # 生成视频文件路径
            clip_dir = os.path.dirname(clip_path)
            clip_name = os.path.splitext(os.path.basename(clip_path))[0]
            video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

            # 创建视频
            video_success = create_video_from_frames(frames_info, video_path)

            if video_success:
                # 创建包含视频信息的新JSON文件
                video_json_path = os.path.join(clip_dir, f"{clip_name}_video.json")
                create_video_json(original_clip_data, frames_info, video_path, video_json_path)
            else:
                print(f"视频创建失败，跳过: {clip_path}")

        except Exception as e:
            print(f"处理clip {clip_path} 时出错: {str(e)}")
            continue

    total_end_time = time.time()
    print(f"\n所有处理完成！总耗时: {total_end_time - total_start_time:.2f} 秒")

if __name__ == "__main__":
    main()
