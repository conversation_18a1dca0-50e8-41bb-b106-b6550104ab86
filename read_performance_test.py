#!/usr/bin/env python3
import json
import os
import random
import time
import cv2
import numpy as np
from typing import List, Tuple, Dict
import sys

class FrameData:
    def __init__(self, clip_path: str, frame_index: int, video_path: str, jpg_path: str, key_frame_index: int):
        self.clip_path = clip_path
        self.frame_index = frame_index
        self.video_path = video_path
        self.jpg_path = jpg_path
        self.key_frame_index = key_frame_index

class VideoReader:
    """视频帧读取器，缓存视频文件句柄"""
    def __init__(self):
        self.video_handles = {}

    def get_frame(self, video_path: str, frame_index: int) -> np.ndarray:
        """从视频读取指定帧"""
        if video_path not in self.video_handles:
            self.video_handles[video_path] = cv2.VideoCapture(video_path)

        cap = self.video_handles[video_path]
        if not cap.isOpened():
            raise Exception(f"无法打开视频文件: {video_path}")

        # 设置到指定帧
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
        ret, frame = cap.read()

        if not ret:
            raise Exception(f"无法读取视频帧: {video_path} 第{frame_index}帧")

        return frame

    def close(self):
        """关闭所有视频句柄"""
        for cap in self.video_handles.values():
            cap.release()
        self.video_handles.clear()

class ReadPerformanceTester:
    def __init__(self):
        self.clips_data = []
        self.frame_data_list = []
        self.video_reader = VideoReader()

    def load_clip_data(self):
        """加载clip数据"""
        print("加载clip数据...")
        with open('local_z10_10.json', 'r') as f:
            paths_data = json.load(f)

        clip_paths = paths_data['paths']

        for clip_path in clip_paths:
            # 读取对应的video json文件
            clip_dir = os.path.dirname(clip_path)
            clip_name = os.path.splitext(os.path.basename(clip_path))[0]
            video_json_path = os.path.join(clip_dir, f"{clip_name}_video.json")

            if not os.path.exists(video_json_path):
                print(f"警告: 找不到video json文件: {video_json_path}")
                continue

            try:
                with open(video_json_path, 'r') as f:
                    video_data = json.load(f)

                video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

                for frame in video_data.get('frames', []):
                    sensor_data = frame.get('sensor_data', {})
                    cam_data = sensor_data.get('cam_front_120', {})

                    if 'video_path' in cam_data and 'file_path' in cam_data:
                        frame_data = FrameData(
                            clip_path=clip_path,
                            frame_index=cam_data.get('frame_index', 0),
                            video_path=cam_data['video_path'],
                            jpg_path=cam_data['file_path'],
                            key_frame_index=cam_data.get('key_frame_index', 0)
                        )
                        self.frame_data_list.append(frame_data)

            except Exception as e:
                print(f"加载clip数据失败: {e}")
                continue

        print(f"总共加载了 {len(self.frame_data_list)} 帧数据")
        return len(self.frame_data_list) > 0

    def read_jpg_frame(self, jpg_path: str) -> np.ndarray:
        """从JPG文件读取图片"""
        if not os.path.exists(jpg_path):
            raise Exception(f"JPG文件不存在: {jpg_path}")

        return cv2.imread(jpg_path)

    def experiment1_random_frames(self, frame_count: int = 100) -> Dict:
        """实验1: 随机读取100帧"""
        print(f"\n=== 实验1: 随机读取{frame_count}帧 ===")

        # 随机选择frame_count帧
        selected_frames = random.sample(self.frame_data_list, min(frame_count, len(self.frame_data_list)))

        # JPG读取测试
        start_time = time.time()
        jpg_results = []
        jpg_errors = 0
        for frame_data in selected_frames:
            try:
                frame = self.read_jpg_frame(frame_data.jpg_path)
                jpg_results.append(frame.shape)
            except Exception as e:
                jpg_errors += 1
                # print(f"JPG读取错误: {e}")
        jpg_time = time.time() - start_time

        # 视频读取测试
        start_time = time.time()
        video_results = []
        video_errors = 0
        for frame_data in selected_frames:
            try:
                frame = self.video_reader.get_frame(frame_data.video_path, frame_data.frame_index)
                video_results.append(frame.shape)
            except Exception as e:
                video_errors += 1
                # print(f"视频读取错误: {e}")
        video_time = time.time() - start_time

        return {
            'experiment': 'random_frames',
            'frame_count': len(selected_frames),
            'jpg_time': jpg_time,
            'video_time': video_time,
            'jpg_errors': jpg_errors,
            'video_errors': video_errors,
            'speedup': jpg_time / video_time if video_time > 0 else 0
        }

    def experiment2_continuous_frames(self, group_size: int = 5, total_groups: int = 20) -> Dict:
        """实验2: 5帧一组连续帧读取"""
        print(f"\n=== 实验2: {group_size}帧一组连续帧读取 ({total_groups}组) ===")

        selected_frames = []

        # 按clip分组
        frames_by_clip = {}
        for frame_data in self.frame_data_list:
            clip_key = frame_data.clip_path
            if clip_key not in frames_by_clip:
                frames_by_clip[clip_key] = []
            frames_by_clip[clip_key].append(frame_data)

        # 为每个clip按frame_index排序
        for clip_key in frames_by_clip:
            frames_by_clip[clip_key].sort(key=lambda x: x.frame_index)

        # 随机选择组和起始帧
        all_groups = []
        for clip_key, frames in frames_by_clip.items():
            if len(frames) >= group_size:
                # 计算可能的起始位置
                max_start = len(frames) - group_size
                for start_pos in range(max_start + 1):
                    group = frames[start_pos:start_pos + group_size]
                    all_groups.append((clip_key, start_pos, group))

        # 随机选择total_groups个组
        selected_groups = random.sample(all_groups, min(total_groups, len(all_groups)))

        for clip_key, start_pos, group in selected_groups:
            selected_frames.extend(group)

        print(f"选择了 {len(selected_groups)} 个连续帧组，共 {len(selected_frames)} 帧")

        # JPG读取测试
        start_time = time.time()
        jpg_results = []
        jpg_errors = 0
        for frame_data in selected_frames:
            try:
                frame = self.read_jpg_frame(frame_data.jpg_path)
                jpg_results.append(frame.shape)
            except Exception as e:
                jpg_errors += 1
        jpg_time = time.time() - start_time

        # 视频读取测试
        start_time = time.time()
        video_results = []
        video_errors = 0
        for frame_data in selected_frames:
            try:
                frame = self.video_reader.get_frame(frame_data.video_path, frame_data.frame_index)
                video_results.append(frame.shape)
            except Exception as e:
                video_errors += 1
        video_time = time.time() - start_time

        return {
            'experiment': 'continuous_frames',
            'groups_count': len(selected_groups),
            'frame_count': len(selected_frames),
            'jpg_time': jpg_time,
            'video_time': video_time,
            'jpg_errors': jpg_errors,
            'video_errors': video_errors,
            'speedup': jpg_time / video_time if video_time > 0 else 0
        }

    def experiment3_keyframe_continuous_frames(self, group_size: int = 5, total_groups: int = 20) -> Dict:
        """实验3: 基于关键帧的连续帧读取"""
        print(f"\n=== 实验3: 基于关键帧的{group_size}帧一组连续帧读取 ({total_groups}组) ===")

        selected_frames = []

        # 按clip分组，找出关键帧
        keyframes_by_clip = {}
        for frame_data in self.frame_data_list:
            clip_key = frame_data.clip_path
            if clip_key not in keyframes_by_clip:
                keyframes_by_clip[clip_key] = []

            # 如果当前帧是关键帧
            if frame_data.frame_index == frame_data.key_frame_index:
                keyframes_by_clip[clip_key].append(frame_data)

        # 为每个clip的关键帧排序
        for clip_key in keyframes_by_clip:
            keyframes_by_clip[clip_key].sort(key=lambda x: x.frame_index)

        # 从关键帧开始构建连续组
        all_groups = []
        for clip_key, keyframes in keyframes_by_clip.items():
            # 获取该clip的所有帧并排序
            clip_frames = [f for f in self.frame_data_list if f.clip_path == clip_key]
            clip_frames.sort(key=lambda x: x.frame_index)

            # 为每个关键帧构建连续帧组
            for keyframe in keyframes:
                # 找到关键帧在clip_frames中的位置
                keyframe_pos = None
                for i, f in enumerate(clip_frames):
                    if f.frame_index == keyframe.frame_index:
                        keyframe_pos = i
                        break

                if keyframe_pos is not None:
                    # 从关键帧位置开始取group_size帧
                    if keyframe_pos + group_size <= len(clip_frames):
                        group = clip_frames[keyframe_pos:keyframe_pos + group_size]
                        all_groups.append((clip_key, keyframe.frame_index, group))

        # 随机选择total_groups个组
        selected_groups = random.sample(all_groups, min(total_groups, len(all_groups)))

        for clip_key, keyframe_index, group in selected_groups:
            selected_frames.extend(group)

        print(f"选择了 {len(selected_groups)} 个基于关键帧的连续帧组，共 {len(selected_frames)} 帧")

        # JPG读取测试
        start_time = time.time()
        jpg_results = []
        jpg_errors = 0
        for frame_data in selected_frames:
            try:
                frame = self.read_jpg_frame(frame_data.jpg_path)
                jpg_results.append(frame.shape)
            except Exception as e:
                jpg_errors += 1
        jpg_time = time.time() - start_time

        # 视频读取测试
        start_time = time.time()
        video_results = []
        video_errors = 0
        for frame_data in selected_frames:
            try:
                frame = self.video_reader.get_frame(frame_data.video_path, frame_data.frame_index)
                video_results.append(frame.shape)
            except Exception as e:
                video_errors += 1
        video_time = time.time() - start_time

        return {
            'experiment': 'keyframe_continuous_frames',
            'groups_count': len(selected_groups),
            'keyframe_groups_count': len(selected_groups),
            'frame_count': len(selected_frames),
            'jpg_time': jpg_time,
            'video_time': video_time,
            'jpg_errors': jpg_errors,
            'video_errors': video_errors,
            'speedup': jpg_time / video_time if video_time > 0 else 0
        }

    def run_all_experiments(self) -> List[Dict]:
        """运行所有实验"""
        print("开始读取性能测试")
        print("=" * 60)

        if not self.load_clip_data():
            print("无法加载clip数据，退出测试")
            return []

        results = []

        # 运行实验1
        results.append(self.experiment1_random_frames(100))

        # 运行实验2
        results.append(self.experiment2_continuous_frames(5, 20))

        # 运行实验3
        results.append(self.experiment3_keyframe_continuous_frames(5, 20))

        # 关闭视频读取器
        self.video_reader.close()

        return results

    def print_results(self, results: List[Dict]):
        """打印测试结果"""
        print("\n" + "=" * 60)
        print("读取性能测试结果")
        print("=" * 60)

        for i, result in enumerate(results, 1):
            print(f"\n实验{i}: {result['experiment']}")
            print(f"  读取帧数: {result['frame_count']}")
            if 'groups_count' in result:
                print(f"  组数: {result['groups_count']}")
            if 'keyframe_groups_count' in result:
                print(f"  关键帧组数: {result['keyframe_groups_count']}")

            print(f"\n  🖼️  JPG读取:")
            print(f"    时间: {result['jpg_time']:.4f}秒")
            print(f"    错误数: {result['jpg_errors']}")
            print(f"    平均每帧: {(result['jpg_time']/result['frame_count'])*1000:.2f}ms")

            print(f"\n  🎬 视频读取:")
            print(f"    时间: {result['video_time']:.4f}秒")
            print(f"    错误数: {result['video_errors']}")
            print(f"    平均每帧: {(result['video_time']/result['frame_count'])*1000:.2f}ms")

            print(f"\n  📊 性能对比:")
            print(f"    加速比: {result['speedup']:.2f}x")
            if result['speedup'] > 1:
                print(f"    视频比JPG快 {result['speedup']:.2f} 倍")
            else:
                print(f"    JPG比视频快 {1/result['speedup']:.2f} 倍")

def main():
    tester = ReadPerformanceTester()
    results = tester.run_all_experiments()

    if results:
        tester.print_results(results)

        # 保存结果到JSON文件
        with open('read_performance_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\n结果已保存到: read_performance_results.json")

if __name__ == "__main__":
    main()