#!/usr/bin/env python3
import json
import os
import random
import time
import cv2
import numpy as np
from typing import List, Tu<PERSON>, Dict

class FrameData:
    def __init__(self, clip_path: str, frame_index: int, video_path: str, jpg_path: str, key_frame_index: int):
        self.clip_path = clip_path
        self.frame_index = frame_index
        self.video_path = video_path
        self.jpg_path = jpg_path
        self.key_frame_index = key_frame_index

def load_frame_data():
    """加载所有帧数据"""
    print("加载clip数据...")
    frame_data_list = []

    with open('local_z10_10.json', 'r') as f:
        paths_data = json.load(f)

    clip_paths = paths_data['paths']

    for clip_path in clip_paths:
        # 读取对应的video json文件
        clip_dir = os.path.dirname(clip_path)
        clip_name = os.path.splitext(os.path.basename(clip_path))[0]
        video_json_path = os.path.join(clip_dir, f"{clip_name}_video.json")

        if not os.path.exists(video_json_path):
            print(f"警告: 找不到video json文件: {video_json_path}")
            continue

        try:
            with open(video_json_path, 'r') as f:
                video_data = json.load(f)

            video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

            for frame in video_data.get('frames', []):
                sensor_data = frame.get('sensor_data', {})
                cam_data = sensor_data.get('cam_front_120', {})

                if 'video_path' in cam_data and 'file_path' in cam_data:
                    frame_data = FrameData(
                        clip_path=clip_path,
                        frame_index=cam_data.get('frame_index', 0),
                        video_path=cam_data['video_path'],
                        jpg_path=cam_data['file_path'],
                        key_frame_index=cam_data.get('key_frame_index', 0)
                    )
                    frame_data_list.append(frame_data)

        except Exception as e:
            print(f"加载clip数据失败: {e}")
            continue

    print(f"总共加载了 {len(frame_data_list)} 帧数据")
    return frame_data_list

def read_jpg_frame(jpg_path: str) -> np.ndarray:
    """从JPG文件读取图片"""
    if not os.path.exists(jpg_path):
        raise Exception(f"JPG文件不存在: {jpg_path}")
    return cv2.imread(jpg_path)

def read_continuous_frames_from_key_gpu(video_path: str, key_frame_index: int, target_frame_index: int, num_frames: int = 5):
    """
    使用GPU加速解码（NVIDIA NVDEC）。
    从指定关键帧开始解码，读取从目标帧起连续的 num_frames 帧。
    要求OpenCV编译时启用了 CUDA 和 cudacodec 模块。
    """
    # 创建GPU视频解码器
    try:
        cap = cv2.cudacodec.createVideoReader(video_path)
    except Exception as e:
        raise RuntimeError(f"无法创建 GPU 视频读取器: {e}")

    current_index = 0
    frames = []
    found_target = False

    while True:
        ret, gpu_frame = cap.nextFrame()
        if not ret:
            break

        # 将 GPU 帧转到 CPU 内存
        frame = gpu_frame.download()

        if current_index >= key_frame_index:
            if current_index >= target_frame_index:
                found_target = True
                frames.append(frame)

            if found_target and len(frames) >= num_frames:
                break

        current_index += 1

    if not found_target:
        raise Exception(f"未找到目标帧: {video_path} 第{target_frame_index}帧")

    return frames

def experiment3_keyframe_continuous_frames_gpu(group_size: int = 5, total_groups: int = 20):
    """实验3 GPU版: 基于关键帧的连续帧读取"""
    print(f"\n=== 实验3 GPU版: 基于关键帧的{group_size}帧一组连续帧读取 ({total_groups}组) ===")
    print("使用NVIDIA GPU加速解码")

    frame_data_list = load_frame_data()
    if len(frame_data_list) == 0:
        print("没有可用的帧数据")
        return

    # 按clip分组，找出关键帧
    keyframes_by_clip = {}
    for frame_data in frame_data_list:
        clip_key = frame_data.clip_path
        if clip_key not in keyframes_by_clip:
            keyframes_by_clip[clip_key] = []

        # 如果当前帧是关键帧
        if frame_data.frame_index == frame_data.key_frame_index:
            keyframes_by_clip[clip_key].append(frame_data)

    # 为每个clip的关键帧排序
    for clip_key in keyframes_by_clip:
        keyframes_by_clip[clip_key].sort(key=lambda x: x.frame_index)

    # 收集所有关键帧
    all_keyframes = []
    for clip_key, keyframes in keyframes_by_clip.items():
        all_keyframes.extend(keyframes)

    print(f"找到 {len(all_keyframes)} 个关键帧")

    # 随机选择total_groups个关键帧
    if len(all_keyframes) < total_groups:
        print(f"关键帧不足，只有 {len(all_keyframes)} 个，无法选择 {total_groups} 组")
        return

    selected_keyframes = random.sample(all_keyframes, total_groups)

    # 为每个选中的关键帧构建连续帧组
    selected_groups = []
    selected_frames = []

    for keyframe in selected_keyframes:
        # 获取该关键帧所在clip的所有帧
        clip_key = keyframe.clip_path
        clip_frames = [f for f in frame_data_list if f.clip_path == clip_key]
        clip_frames.sort(key=lambda x: x.frame_index)

        # 找到关键帧在clip_frames中的位置
        keyframe_pos = None
        for i, f in enumerate(clip_frames):
            if f.frame_index == keyframe.frame_index:
                keyframe_pos = i
                break

        if keyframe_pos is not None:
            # 从关键帧位置开始取group_size帧
            if keyframe_pos + group_size <= len(clip_frames):
                group = clip_frames[keyframe_pos:keyframe_pos + group_size]
                selected_groups.append((clip_key, keyframe.frame_index, group))
                selected_frames.extend(group)
            else:
                print(f"关键帧 {keyframe.frame_index} 位置 {keyframe_pos}，无法组成 {group_size} 帧组")
        else:
            print(f"未找到关键帧 {keyframe.frame_index}")

    print(f"选择了 {len(selected_groups)} 个基于关键帧的连续帧组，共 {len(selected_frames)} 帧")

    # 统计关键帧数量
    keyframe_count = sum(1 for f in selected_frames if f.frame_index == f.key_frame_index)
    print(f"其中关键帧: {keyframe_count} 个，非关键帧: {len(selected_frames)-keyframe_count} 个")

    # JPG读取测试
    print("\n🖼️  JPG读取测试...")
    start_time = time.time()
    jpg_results = []
    jpg_errors = 0
    for i, frame_data in enumerate(selected_frames):
        try:
            frame = read_jpg_frame(frame_data.jpg_path)
            jpg_results.append(frame.shape)
            if (i + 1) % 25 == 0:
                print(f"  已读取 {i + 1}/{len(selected_frames)} 帧")
        except Exception as e:
            jpg_errors += 1
    jpg_time = time.time() - start_time

    print(f"JPG读取完成: {jpg_time:.4f}秒, 成功 {len(jpg_results)} 帧, 失败 {jpg_errors} 帧")

    # 视频读取测试 - 使用GPU加速的连续帧读取
    print("\n🎬 视频读取测试（GPU加速版：基于关键帧的连续帧批量读取）...")
    start_time = time.time()
    video_results = []
    video_errors = 0

    # 按(视频路径, 关键帧)分组进行GPU批量读取
    frame_groups = {}
    for i, frame_data in enumerate(selected_frames):
        group_key = (frame_data.video_path, frame_data.key_frame_index)
        if group_key not in frame_groups:
            frame_groups[group_key] = []
        frame_groups[group_key].append((i, frame_data))

    for (video_path, key_frame_index), group_items in frame_groups.items():
        # 找到最小的目标帧作为起始点
        min_target_frame = min(item[1].frame_index for item in group_items)

        try:
            # 使用GPU批量读取从最小目标帧开始的连续帧
            frames_batch = read_continuous_frames_from_key_gpu(
                video_path, key_frame_index, min_target_frame, group_size
            )

            print(f"  GPU读取组: {video_path} 关键帧{key_frame_index} -> 帧{min_target_frame}")

            # 将读取的帧分配给对应的索引
            for idx, (result_idx, frame_data) in enumerate(group_items):
                relative_index = frame_data.frame_index - min_target_frame
                if relative_index < len(frames_batch):
                    video_results.append(frames_batch[relative_index].shape)
                else:
                    video_results.append(None)
                    video_errors += 1

        except Exception as e:
            # 如果GPU批量读取失败，记录错误
            for i, (result_idx, frame_data) in enumerate(group_items):
                video_results.append(None)
                video_errors += 1
            print(f"  GPU批量读取失败组 {video_path}:{key_frame_index}: {e}")

    # 确保video_results长度正确
    while len(video_results) < len(selected_frames):
        video_results.append(None)
        video_errors += 1

    video_time = time.time() - start_time

    print(f"GPU视频读取完成: {video_time:.4f}秒, 成功 {len(video_results)-video_errors} 帧, 失败 {video_errors} 帧")

    # 结果分析
    print(f"\n📊 实验3 GPU版结果:")
    print(f"  关键帧组数: {len(selected_groups)}")
    print(f"  总帧数: {len(selected_frames)}")
    print(f"  关键帧数: {keyframe_count}")
    print(f"  JPG时间: {jpg_time:.4f}秒")
    print(f"  GPU视频时间: {video_time:.4f}秒")
    print(f"  JPG平均每帧: {(jpg_time/len(selected_frames))*1000:.2f}ms")
    print(f"  GPU视频平均每帧: {(video_time/len(selected_frames))*1000:.2f}ms")

    speedup = jpg_time / video_time if video_time > 0 else 0
    if speedup > 1:
        print(f"  🚀 GPU视频比JPG快 {speedup:.2f} 倍")
        print(f"  ⚡ GPU加速效果: 显著提升")
    else:
        print(f"  🐌 JPG比GPU视频快 {1/speedup:.2f} 倍")

    # 保存结果
    result = {
        'experiment': 'experiment3_gpu_optimized',
        'keyframe_groups_count': len(selected_groups),
        'frame_count': len(selected_frames),
        'keyframe_count': keyframe_count,
        'jpg_time': jpg_time,
        'gpu_video_time': video_time,
        'jpg_errors': jpg_errors,
        'gpu_video_errors': video_errors,
        'jpg_avg_ms': (jpg_time/len(selected_frames))*1000,
        'gpu_video_avg_ms': (video_time/len(selected_frames))*1000,
        'speedup': speedup
    }

    with open('experiment3_gpu_result.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    print(f"\n📄 GPU实验结果已保存到: experiment3_gpu_result.json")
    return result

def main():
    print("实验3 GPU版读取性能测试")
    print("=" * 60)
    print("GPU加速策略:")
    print("- 使用NVIDIA CUDA加速解码")
    print("- GPU内存中解码，然后下载到CPU")
    print("- 基于关键帧的连续帧批量读取")
    print("=" * 60)

    # 检查GPU支持
    try:
        # 检查CUDA是否可用
        import cv2
        if hasattr(cv2, 'cudacodec'):
            print("✅ 检测到OpenCV CUDA支持")
        else:
            print("❌ OpenCV未编译CUDA支持，将使用CPU版本")
            print("💡 需要重新编译OpenCV并启用cudacodec模块")
    except Exception as e:
        print(f"❌ 检查GPU支持失败: {e}")

    result = experiment3_keyframe_continuous_frames_gpu(5, 20)

if __name__ == "__main__":
    main()