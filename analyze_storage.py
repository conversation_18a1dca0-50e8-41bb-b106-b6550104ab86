#!/usr/bin/env python3
import json
import os
import glob
from pathlib import Path

def get_file_size(file_path):
    """获取文件大小（字节）"""
    try:
        return os.path.getsize(file_path)
    except OSError:
        return 0

def format_size(size_bytes):
    """格式化文件大小为可读格式"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def analyze_video_storage():
    """分析视频存储占用"""
    print("=== 视频存储分析 ===")

    # 查找所有视频文件
    video_files = []
    for pattern in [
        "json/**/*_3d2d_renori.mp4",
        "json/**/*.mp4"
    ]:
        video_files.extend(glob.glob(pattern, recursive=True))

    # 去重
    video_files = list(set(video_files))

    print(f"找到 {len(video_files)} 个视频文件")

    total_video_size = 0
    video_sizes = []

    for video_file in video_files:
        size = get_file_size(video_file)
        total_video_size += size
        video_sizes.append((video_file, size))
        print(f"  {os.path.basename(video_file)}: {format_size(size)}")

    avg_video_size = total_video_size / len(video_files) if video_files else 0

    print(f"\n视频存储统计:")
    print(f"  总视频文件数: {len(video_files)}")
    print(f"  总视频大小: {format_size(total_video_size)}")
    print(f"  平均每个视频大小: {format_size(avg_video_size)}")

    return len(video_files), total_video_size, avg_video_size

def analyze_jpg_storage():
    """分析JPG图片存储占用"""
    print("\n=== JPG图片存储分析 ===")

    # 查找所有JPG文件
    jpg_files = []
    for pattern in [
        "jpg/**/*.jpg",
        "jpg/**/*.jpeg"
    ]:
        jpg_files.extend(glob.glob(pattern, recursive=True))

    # 去重
    jpg_files = list(set(jpg_files))

    print(f"找到 {len(jpg_files)} 个JPG文件")

    total_jpg_size = 0
    jpg_sizes = []

    for jpg_file in jpg_files:
        size = get_file_size(jpg_file)
        total_jpg_size += size
        jpg_sizes.append((jpg_file, size))

    avg_jpg_size = total_jpg_size / len(jpg_files) if jpg_files else 0

    print(f"\nJPG存储统计:")
    print(f"  总JPG文件数: {len(jpg_files)}")
    print(f"  总JPG大小: {format_size(total_jpg_size)}")
    print(f"  平均每个JPG大小: {format_size(avg_jpg_size)}")

    return len(jpg_files), total_jpg_size, avg_jpg_size

def analyze_clip_jpg_storage():
    """按clip分析JPG存储（基于clip中的文件路径）"""
    print("\n=== Clip JPG存储分析（基于JSON数据）===")

    try:
        with open('local_z10_10.json', 'r') as f:
            paths_data = json.load(f)

        clip_paths = paths_data['paths']
        total_clip_jpg_size = 0
        clip_count = 0
        valid_clips = 0

        # 统计每个clip的JPG大小
        for clip_path in clip_paths:
            clip_count += 1

            try:
                with open(clip_path, 'r') as f:
                    clip_data = json.load(f)

                clip_jpg_size = 0
                frame_count = 0

                for frame in clip_data.get('frames', []):
                    sensor_data = frame.get('sensor_data', {})
                    cam_data = sensor_data.get('cam_front_120', {})

                    if 'file_path' in cam_data:
                        # 转换s3路径为本地路径
                        original_path = cam_data['file_path']
                        if original_path.startswith("s3://"):
                            local_path = f"/data/workspace/vframe/jpg/{original_path[5:]}"
                        else:
                            local_path = original_path

                        jpg_size = get_file_size(local_path)
                        clip_jpg_size += jpg_size
                        frame_count += 1

                if frame_count > 0:
                    valid_clips += 1
                    total_clip_jpg_size += clip_jpg_size
                    print(f"  Clip {clip_count}/{len(clip_paths)} ({os.path.basename(clip_path)}): {frame_count}帧, {format_size(clip_jpg_size)}")

            except Exception as e:
                print(f"  Clip {clip_count}/{len(clip_paths)} 处理失败: {e}")

        avg_clip_jpg_size = total_clip_jpg_size / valid_clips if valid_clips > 0 else 0

        print(f"\nClip JPG存储统计:")
        print(f"  总clip数: {clip_count}")
        print(f"  有效clip数: {valid_clips}")
        print(f"  总Clip JPG大小: {format_size(total_clip_jpg_size)}")
        print(f"  平均每个Clip JPG大小: {format_size(avg_clip_jpg_size)}")

        return valid_clips, total_clip_jpg_size, avg_clip_jpg_size

    except Exception as e:
        print(f"分析Clip JPG存储失败: {e}")
        return 0, 0, 0

def main():
    print("存储性能对比分析")
    print("=" * 50)

    # 分析视频存储
    video_count, total_video_size, avg_video_size = analyze_video_storage()

    # 分析所有JPG存储
    jpg_count, total_jpg_size, avg_jpg_size = analyze_jpg_storage()

    # 分析Clip相关JPG存储
    clip_count, total_clip_jpg_size, avg_clip_jpg_size = analyze_clip_jpg_storage()

    # 对比分析
    print("\n" + "=" * 50)
    print("存储性能对比")
    print("=" * 50)

    print(f"\n📊 数量对比:")
    print(f"  视频文件数: {video_count}")
    print(f"  Clip相关JPG文件数: {clip_count} (按clip统计)")
    print(f"  所有JPG文件数: {jpg_count}")

    print(f"\n💾 存储空间对比:")
    print(f"  视频总存储: {format_size(total_video_size)}")
    print(f"  Clip JPG总存储: {format_size(total_clip_jpg_size)}")
    print(f"  所有JPG总存储: {format_size(total_jpg_size)}")

    print(f"\n📈 平均大小对比:")
    print(f"  平均每个视频: {format_size(avg_video_size)}")
    print(f"  平均每个Clip JPG: {format_size(avg_clip_jpg_size)}")
    print(f"  平均每个JPG文件: {format_size(avg_jpg_size)}")

    # 压缩率计算
    if total_clip_jpg_size > 0:
        compression_ratio = total_clip_jpg_size / total_video_size
        print(f"\n🎯 压缩性能:")
        print(f"  JPG -> H265 压缩比: {compression_ratio:.2f}:1")
        print(f"  空间节省: {(1 - 1/compression_ratio)*100:.1f}%")

    if video_count > 0 and clip_count > 0:
        print(f"\n📊 按Clip对比:")
        print(f"  Clip数量: {clip_count}")
        print(f"  视频数量: {video_count}")
        print(f"  转换成功率: {(video_count/clip_count)*100:.1f}%")

if __name__ == "__main__":
    main()