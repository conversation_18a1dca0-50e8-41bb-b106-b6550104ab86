#!/usr/bin/env python3
import cv2
import av
import numpy as np
import os

def test_frame_reading_accuracy():
    """测试PyAV和OpenCV读取帧的准确性问题"""

    # 找一个视频文件
    import json
    with open('local_z10_10.json', 'r') as f:
        paths_data = json.load(f)

    clip_paths = paths_data['paths']
    clip_path = clip_paths[0]
    clip_dir = os.path.dirname(clip_path)
    clip_name = os.path.splitext(os.path.basename(clip_path))[0]
    video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

    print(f"测试视频: {video_path}")

    # 获取视频信息
    container = av.open(video_path)
    stream = container.streams.video[0]

    print(f"视频总帧数 (PyAV): {stream.frames}")
    print(f"视频时长 (秒): {stream.duration * stream.time_base}")
    print(f"帧率: {stream.average_rate}")

    cap = cv2.VideoCapture(video_path)
    total_frames_opencv = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps_opencv = cap.get(cv2.CAP_PROP_FPS)
    print(f"视频总帧数 (OpenCV): {total_frames_opencv}")
    print(f"帧率 (OpenCV): {fps_opencv}")
    cap.release()

    # 测试连续读取前10帧
    print("\n--- 测试连续读取前10帧 ---")

    # OpenCV连续读取
    cap = cv2.VideoCapture(video_path)
    opencv_frames = []
    for i in range(10):
        ret, frame = cap.read()
        if ret:
            opencv_frames.append(frame)
        else:
            print(f"OpenCV读取第{i}帧失败")
            break
    cap.release()
    print(f"OpenCV成功读取 {len(opencv_frames)} 帧")

    # PyAV连续读取
    container = av.open(video_path)
    stream = container.streams.video[0]
    pyav_frames = []
    frame_count = 0

    for packet in container.demux(stream):
        for frame in packet.decode():
            if frame_count >= 10:
                break
            img = frame.to_ndarray(format="bgr24")
            pyav_frames.append(img)
            frame_count += 1
        if frame_count >= 10:
            break

    container.close()
    print(f"PyAV成功读取 {len(pyav_frames)} 帧")

    # 比较连续读取的帧
    print("\n--- 比较连续读取的帧 ---")
    for i in range(min(len(opencv_frames), len(pyav_frames))):
        frame1 = opencv_frames[i]
        frame2 = pyav_frames[i]

        diff = np.abs(frame1.astype(np.int16) - frame2.astype(np.int16))
        max_diff = np.max(diff)
        mean_diff = np.mean(diff)

        print(f"第{i}帧: 最大差异 {max_diff}, 平均差异 {mean_diff:.2f}")

        if max_diff > 5:
            print(f"  ⚠️ 第{i}帧差异较大")

            # 检查是否可能是时间戳问题
            if i == 0:
                print(f"  第0帧就存在差异，可能是解码器或色彩空间转换差异")

    # 测试关键帧对齐
    print("\n--- 测试关键帧对齐 ---")

    # 重新打开视频，找到关键帧
    container = av.open(video_path)
    stream = container.streams.video[0]

    keyframes = []
    for packet in container.demux(stream):
        for frame in packet.decode():
            if frame.key_frame:
                keyframes.append(frame.index)
                print(f"发现关键帧: {frame.index}")
                if len(keyframes) >= 3:
                    break
        if len(keyframes) >= 3:
            break

    container.close()

    # 测试这些关键帧是否与OpenCV对齐
    for keyframe_idx in keyframes[:3]:
        print(f"\n测试关键帧 {keyframe_idx}:")

        # OpenCV读取
        cap = cv2.VideoCapture(video_path)
        cap.set(cv2.CAP_PROP_POS_FRAMES, keyframe_idx)
        ret, frame_ocv = cap.read()
        cap.release()

        if ret:
            print(f"  OpenCV读取成功: {frame_ocv.shape}")
        else:
            print(f"  OpenCV读取失败")
            continue

        # PyAV读取关键帧
        container = av.open(video_path)
        stream = container.streams.video[0]

        # Seek到关键帧位置
        frame_pts = int(keyframe_idx * stream.duration / stream.frames)
        container.seek(frame_pts, stream=stream)

        current_index = 0
        found = False
        frame_pyav = None

        for packet in container.demux(stream):
            for frame in packet.decode():
                if current_index == keyframe_idx:
                    frame_pyav = frame.to_ndarray(format="bgr24")
                    found = True
                    break
                current_index += 1
            if found:
                break

        container.close()

        if found:
            print(f"  PyAV读取成功: {frame_pyav.shape}")

            # 比较关键帧
            diff = np.abs(frame_ocv.astype(np.int16) - frame_pyav.astype(np.int16))
            max_diff = np.max(diff)
            mean_diff = np.mean(diff)
            print(f"  关键帧差异: 最大 {max_diff}, 平均 {mean_diff:.2f}")

            if max_diff <= 2:
                print(f"  ✅ 关键帧 {keyframe_idx} 数据一致")
            else:
                print(f"  ❌ 关键帧 {keyframe_idx} 数据不一致")
        else:
            print(f"  PyAV读取失败")

def test_timestamp_conversion():
    """测试时间戳转换的准确性"""

    import json
    with open('local_z10_10.json', 'r') as f:
        paths_data = json.load(f)

    clip_paths = paths_data['paths']
    clip_path = clip_paths[0]
    clip_dir = os.path.dirname(clip_path)
    clip_name = os.path.splitext(os.path.basename(clip_path))[0]
    video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

    print(f"\n=== 测试时间戳转换: {os.path.basename(video_path)} ===")

    container = av.open(video_path)
    stream = container.streams.video[0]

    total_frames = stream.frames
    duration = stream.duration
    time_base = stream.time_base

    print(f"总帧数: {total_frames}")
    print(f"时长: {duration * time_base:.2f}秒")
    print(f"时间基: {time_base}")

    # 测试几个不同帧的时间戳转换
    test_frames = [0, total_frames//4, total_frames//2, total_frames-1]

    for frame_idx in test_frames:
        # 方法1: 基于帧数的线性插值
        pts1 = int(frame_idx * duration / total_frames)

        # 方法2: 基于帧率的计算
        fps = stream.average_rate
        pts2 = int(frame_idx / fps * time_base.denominator / time_base.numerator)

        print(f"帧 {frame_idx}:")
        print(f"  线性插值PTS: {pts1}")
        print(f"  帧率计算PTS: {pts2}")
        print(f"  时间 (秒): {pts1 * time_base:.3f}")

    container.close()

if __name__ == "__main__":
    test_frame_reading_accuracy()
    test_timestamp_conversion()