#!/bin/bash
# Auto-generated download script for JSON files from S3/OSS
# 使用AWS CLI with custom endpoint for MachDrive

echo "开始下载1000个JSON文件..."
echo "目标目录: json/"
echo "----------------------------------------"

# 读取JSON文件并生成下载命令
python3 << 'EOF'
import json
import sys
import os
import signal

# 设置信号处理，允许Ctrl+C中断
def signal_handler(sig, frame):
    print("\n接收到中断信号，正在停止下载...")
    sys.exit(1)

signal.signal(signal.SIGINT, signal_handler)

# 读取JSON文件
try:
    with open('hdmap_z10_1000.json', 'r') as f:
        data = json.load(f)
except FileNotFoundError:
    print("错误: 找不到 hdmap_z10_1000.json 文件")
    sys.exit(1)
except json.JSONDecodeError:
    print("错误: JSON文件格式不正确")
    sys.exit(1)

paths = data.get('paths', [])
total_files = len(paths)

if total_files == 0:
    print("警告: JSON文件中没有找到路径")
    sys.exit(0)

print(f"找到 {total_files} 个文件需要下载")
print()

# 生成并执行aws s3 cp命令
for i, path in enumerate(paths, 1):
    try:
        if not path.startswith('s3://'):
            print(f"跳过无效路径: {path}")
            continue

        # 移除s3://前缀，获取相对路径
        relative_path = path[5:]

        # 目标路径
        target_path = f'json/{relative_path}'

        # 创建aws s3 cp命令
        cmd = f'aws --endpoint=http://oss.i.machdrive.cn s3 cp "{path}" "{target_path}"'

        # 显示进度
        print(f"[{i}/{total_files}] 下载中...")
        print(f"源: {path}")
        print(f"目标: {target_path}")

        # 执行命令
        result = os.system(cmd)

        if result == 0:
            print(f"✓ 下载成功")
        else:
            print(f"✗ 下载失败 (退出码: {result})")

        print("-" * 50)

        # 检查是否收到中断信号
        if hasattr(signal, 'SIGINT'):
            pass

    except KeyboardInterrupt:
        print("\n用户中断下载")
        sys.exit(1)
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        continue

print()
print("下载完成!")
EOF