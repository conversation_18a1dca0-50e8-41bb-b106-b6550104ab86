#!/usr/bin/env python3
import json
import os
import random
import time
import cv2
import numpy as np
import psutil
import gc
from typing import List, Tuple, Dict

class FrameData:
    def __init__(self, clip_path: str, frame_index: int, video_path: str, jpg_path: str, key_frame_index: int):
        self.clip_path = clip_path
        self.frame_index = frame_index
        self.video_path = video_path
        self.jpg_path = jpg_path
        self.key_frame_index = key_frame_index

class ResourceMonitor:
    """系统资源监控器"""
    def __init__(self):
        self.process = psutil.Process()
        self.snapshots = []

    def take_snapshot(self, label: str):
        """拍摄系统资源快照"""
        memory_info = self.process.memory_info()
        cpu_percent = self.process.cpu_percent()

        snapshot = {
            'label': label,
            'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
            'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
            'memory_percent': self.process.memory_percent(),  # 内存占用百分比
            'cpu_percent': cpu_percent,  # CPU使用率
            'timestamp': time.time()
        }
        self.snapshots.append(snapshot)
        return snapshot

    def get_peak_memory(self):
        """获取峰值内存"""
        if not self.snapshots:
            return None
        max_snapshot = max(self.snapshots, key=lambda x: x['rss_mb'])
        return max_snapshot

    def get_peak_cpu(self):
        """获取峰值CPU"""
        if not self.snapshots:
            return None
        max_snapshot = max(self.snapshots, key=lambda x: x['cpu_percent'])
        return max_snapshot

    def print_summary(self):
        """打印资源使用摘要"""
        print(f"\n📊 系统资源使用统计:")
        if self.snapshots:
            rss_values = [s['rss_mb'] for s in self.snapshots]
            cpu_values = [s['cpu_percent'] for s in self.snapshots]

            print(f"\n💾 内存使用:")
            print(f"  使用范围: {min(rss_values):.1f} - {max(rss_values):.1f} MB")
            print(f"  平均内存: {np.mean(rss_values):.1f} MB")
            print(f"  内存峰值: {max(rss_values):.1f} MB")

            print(f"\n🖥️  CPU使用:")
            print(f"  使用范围: {min(cpu_values):.1f}% - {max(cpu_values):.1f}%")
            print(f"  平均CPU: {np.mean(cpu_values):.1f}%")
            print(f"  CPU峰值: {max(cpu_values):.1f}%")

            # 显示详细的资源快照
            print(f"\n📈 详细资源快照:")
            for snapshot in self.snapshots:
                print(f"  {snapshot['label']}: 内存{snapshot['rss_mb']:.1f}MB({snapshot['memory_percent']:.1f}%) CPU{snapshot['cpu_percent']:.1f}%")

def load_frame_data():
    """加载所有帧数据"""
    print("加载clip数据...")
    frame_data_list = []

    with open('local_z10_10.json', 'r') as f:
        paths_data = json.load(f)

    clip_paths = paths_data['paths']

    for clip_path in clip_paths:
        # 读取对应的video json文件
        clip_dir = os.path.dirname(clip_path)
        clip_name = os.path.splitext(os.path.basename(clip_path))[0]
        video_json_path = os.path.join(clip_dir, f"{clip_name}_video.json")

        if not os.path.exists(video_json_path):
            print(f"警告: 找不到video json文件: {video_json_path}")
            continue

        try:
            with open(video_json_path, 'r') as f:
                video_data = json.load(f)

            video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

            for frame in video_data.get('frames', []):
                sensor_data = frame.get('sensor_data', {})
                cam_data = sensor_data.get('cam_front_120', {})

                if 'video_path' in cam_data and 'file_path' in cam_data:
                    frame_data = FrameData(
                        clip_path=clip_path,
                        frame_index=cam_data.get('frame_index', 0),
                        video_path=cam_data['video_path'],
                        jpg_path=cam_data['file_path'],
                        key_frame_index=cam_data.get('key_frame_index', 0)
                    )
                    frame_data_list.append(frame_data)

        except Exception as e:
            print(f"加载clip数据失败: {e}")
            continue

    print(f"总共加载了 {len(frame_data_list)} 帧数据")
    return frame_data_list

def read_jpg_frame(jpg_path: str) -> np.ndarray:
    """从JPG文件读取图片"""
    if not os.path.exists(jpg_path):
        raise Exception(f"JPG文件不存在: {jpg_path}")
    return cv2.imread(jpg_path)

def read_continuous_frames_from_key(video_path: str, key_frame_index: int, target_frame_index: int, num_frames: int = 5):
    """
    从指定关键帧开始解码，读取从目标帧起连续的 num_frames 帧。
    若目标帧之前为关键帧，可避免重复解码整个视频。
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise Exception(f"无法打开视频文件: {video_path}")

    # 先定位到关键帧
    cap.set(cv2.CAP_PROP_POS_FRAMES, key_frame_index)

    current_index = key_frame_index
    buffer = []
    found_target = False

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        if current_index >= target_frame_index:
            found_target = True
            buffer.append(frame)

        # 收集完目标帧起连续 num_frames 帧
        if found_target and len(buffer) >= num_frames:
            break

        current_index += 1

    cap.release()

    if not found_target:
        raise Exception(f"未找到目标帧: {video_path} 第{target_frame_index}帧")

    return buffer

def test_jpg_memory_usage(selected_frames: List[FrameData], monitor: ResourceMonitor):
    """测试JPG读取的内存使用"""
    print(f"\n🖼️  JPG内存使用测试...")

    # 开始前快照
    monitor.take_snapshot("JPG开始前")

    start_time = time.time()
    jpg_results = []

    for i, frame_data in enumerate(selected_frames):
        frame = read_jpg_frame(frame_data.jpg_path)
        jpg_results.append(frame.shape)

        # 每25帧拍摄一次内存快照
        if (i + 1) % 25 == 0:
            monitor.take_snapshot(f"JPG读取{i+1}帧")

    jpg_time = time.time() - start_time

    # 读取完成后快照
    monitor.take_snapshot("JPG读取完成")

    # 显式释放内存
    del jpg_results
    gc.collect()
    time.sleep(2)  # 等待内存释放

    monitor.take_snapshot("JPG释放后")

    print(f"JPG读取完成: {jpg_time:.4f}秒, 成功 {len(jpg_results)} 帧")

    return jpg_time, 0

def test_video_memory_usage(selected_frames: List[FrameData], monitor: ResourceMonitor):
    """测试视频读取的内存使用"""
    print(f"\n🎬 视频内存使用测试...")

    # 开始前快照
    monitor.take_snapshot("视频开始前")

    start_time = time.time()
    video_results = []

    if selected_frames:
        # 按(视频路径, 关键帧)分组进行批量读取
        frame_groups = {}
        for i, frame_data in enumerate(selected_frames):
            group_key = (frame_data.video_path, frame_data.key_frame_index)
            if group_key not in frame_groups:
                frame_groups[group_key] = []
            frame_groups[group_key].append((i, frame_data))

        for (video_path, key_frame_index), group_items in frame_groups.items():
            # 找到最小的目标帧作为起始点
            min_target_frame = min(item[1].frame_index for item in group_items)

            # 批量读取前快照
            monitor.take_snapshot(f"读取组{video_path}前")

            # 批量读取从最小目标帧开始的连续帧
            frames_batch = read_continuous_frames_from_key(
                video_path, key_frame_index, min_target_frame, 5
            )

            # 读取完成后快照
            monitor.take_snapshot(f"读取组{video_path}后")

            # 将读取的帧分配给对应的索引
            for idx, (result_idx, frame_data) in enumerate(group_items):
                relative_index = frame_data.frame_index - min_target_frame
                if relative_index < len(frames_batch):
                    video_results.append(frames_batch[relative_index].shape)
                else:
                    video_results.append(None)

    video_time = time.time() - start_time

    # 读取完成后快照
    monitor.take_snapshot("视频读取完成")

    # 显式释放内存
    del video_results
    gc.collect()
    time.sleep(2)  # 等待内存释放

    monitor.take_snapshot("视频释放后")

    print(f"视频读取完成: {video_time:.4f}秒, 成功 {len(video_results)} 帧")

    return video_time, 0

def experiment3_resource_comparison(group_size: int = 5, total_groups: int = 20):
    """实验3: JPG vs 视频内存使用对比"""
    print(f"\n=== 实验3: JPG vs 视频内存使用对比 ({group_size}帧/组, {total_groups}组) ===")

    frame_data_list = load_frame_data()
    if len(frame_data_list) == 0:
        print("没有可用的帧数据")
        return

    # 按clip分组，找出关键帧
    keyframes_by_clip = {}
    for frame_data in frame_data_list:
        clip_key = frame_data.clip_path
        if clip_key not in keyframes_by_clip:
            keyframes_by_clip[clip_key] = []

        # 如果当前帧是关键帧
        if frame_data.frame_index == frame_data.key_frame_index:
            keyframes_by_clip[clip_key].append(frame_data)

    # 为每个clip的关键帧排序
    for clip_key in keyframes_by_clip:
        keyframes_by_clip[clip_key].sort(key=lambda x: x.frame_index)

    # 收集所有关键帧
    all_keyframes = []
    for clip_key, keyframes in keyframes_by_clip.items():
        all_keyframes.extend(keyframes)

    print(f"找到 {len(all_keyframes)} 个关键帧")

    # 随机选择total_groups个关键帧
    if len(all_keyframes) < total_groups:
        print(f"关键帧不足，只有 {len(all_keyframes)} 个，无法选择 {total_groups} 组")
        return

    selected_keyframes = random.sample(all_keyframes, total_groups)

    # 为每个选中的关键帧构建连续帧组
    selected_groups = []
    selected_frames = []

    for keyframe in selected_keyframes:
        # 获取该关键帧所在clip的所有帧
        clip_key = keyframe.clip_path
        clip_frames = [f for f in frame_data_list if f.clip_path == clip_key]
        clip_frames.sort(key=lambda x: x.frame_index)

        # 找到关键帧在clip_frames中的位置
        keyframe_pos = None
        for i, f in enumerate(clip_frames):
            if f.frame_index == keyframe.frame_index:
                keyframe_pos = i
                break

        if keyframe_pos is not None:
            # 从关键帧位置开始取group_size帧
            if keyframe_pos + group_size <= len(clip_frames):
                group = clip_frames[keyframe_pos:keyframe_pos + group_size]
                selected_groups.append((clip_key, keyframe.frame_index, group))
                selected_frames.extend(group)
            else:
                print(f"关键帧 {keyframe.frame_index} 位置 {keyframe_pos}，无法组成 {group_size} 帧组")
        else:
            print(f"未找到关键帧 {keyframe.frame_index}")

    print(f"选择了 {len(selected_groups)} 个基于关键帧的连续帧组，共 {len(selected_frames)} 帧")

    # 统计关键帧数量
    keyframe_count = sum(1 for f in selected_frames if f.frame_index == f.key_frame_index)
    print(f"其中关键帧: {keyframe_count} 个，非关键帧: {len(selected_frames)-keyframe_count} 个")

    # 测试JPG资源使用
    monitor = ResourceMonitor()
    jpg_time, jpg_errors = test_jpg_memory_usage(selected_frames, monitor)

    # 清理内存，等待一段时间
    time.sleep(5)
    monitor = ResourceMonitor()  # 重新创建监控器

    # 测试视频资源使用
    video_time, video_errors = test_video_memory_usage(selected_frames, monitor)

    # 打印资源使用对比
    print(f"\n" + "=" * 80)
    print("系统资源使用对比分析")
    print("=" * 80)

    # 计算峰值资源差
    jpg_snapshots = [s for s in monitor.snapshots if 'JPG' in s['label']]
    video_snapshots = [s for s in monitor.snapshots if '视频' in s['label']]

    jpg_peak_memory = max([s['rss_mb'] for s in jpg_snapshots]) if jpg_snapshots else 0
    video_peak_memory = max([s['rss_mb'] for s in video_snapshots]) if video_snapshots else 0

    jpg_peak_cpu = max([s['cpu_percent'] for s in jpg_snapshots]) if jpg_snapshots else 0
    video_peak_cpu = max([s['cpu_percent'] for s in video_snapshots]) if video_snapshots else 0

    # 内存对比
    if jpg_peak_memory and video_peak_memory:
        memory_diff = video_peak_memory - jpg_peak_memory
        print(f"\n📊 峰值内存对比:")
        print(f"  JPG峰值内存: {jpg_peak_memory:.1f} MB")
        print(f"  视频峰值内存: {video_peak_memory:.1f} MB")
        print(f"  内存差异: {memory_diff:+.1f} MB")

        if memory_diff > 0:
            print(f"  📈 视频比JPG多使用 {memory_diff:.1f} MB ({(memory_diff/jpg_peak_memory)*100:.1f}%)")
        else:
            print(f"  📉 视频比JPG少使用 {-memory_diff:.1f} MB ({(-memory_diff/jpg_peak_memory)*100:.1f}%)")

    # CPU对比
    if jpg_peak_cpu and video_peak_cpu:
        cpu_diff = video_peak_cpu - jpg_peak_cpu
        print(f"\n🖥️  峰值CPU对比:")
        print(f"  JPG峰值CPU: {jpg_peak_cpu:.1f}%")
        print(f"  视频峰值CPU: {video_peak_cpu:.1f}%")
        print(f"  CPU差异: {cpu_diff:+.1f}%")

        if cpu_diff > 0:
            print(f"  🔥 视频比JPG多使用 {cpu_diff:.1f}% CPU")
        else:
            print(f"  ❄️  视频比JPG少使用 {-cpu_diff:.1f}% CPU")

    # 性能对比
    speedup = jpg_time / video_time if video_time > 0 else 0
    print(f"\n⏱️  性能对比:")
    print(f"  JPG时间: {jpg_time:.4f}秒")
    print(f"  视频时间: {video_time:.4f}秒")
    print(f"  加速比: {speedup:.2f}x")

    # 详细资源快照
    monitor.print_summary()

    # 保存结果
    result = {
        'experiment': 'experiment3_resource_comparison',
        'groups_count': len(selected_groups),
        'frame_count': len(selected_frames),
        'keyframe_count': keyframe_count,
        'jpg_time': jpg_time,
        'video_time': video_time,
        'jpg_errors': jpg_errors,
        'video_errors': video_errors,
        'jpg_avg_ms': (jpg_time/len(selected_frames))*1000,
        'video_avg_ms': (video_time/len(selected_frames))*1000,
        'speedup': speedup,
        'resource_snapshots': monitor.snapshots,
        'jpg_peak_memory': jpg_peak_memory,
        'video_peak_memory': video_peak_memory,
        'jpg_peak_cpu': jpg_peak_cpu,
        'video_peak_cpu': video_peak_cpu,
        'memory_difference': (video_peak_memory - jpg_peak_memory) if (jpg_peak_memory and video_peak_memory) else 0,
        'cpu_difference': cpu_diff if (jpg_peak_cpu and video_peak_cpu) else 0
    }

    with open('experiment3_resource_comparison_result.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    print(f"\n📄 系统资源对比结果已保存到: experiment3_resource_comparison_result.json")
    return result

def main():
    print("实验3系统资源对比测试")
    print("=" * 60)
    print("对比内容:")
    print("- JPG文件读取的内存和CPU使用")
    print("- 视频解码的内存和CPU使用")
    print("- 峰值内存和CPU占用对比")
    print("=" * 60)

    # 检查psutil是否可用
    try:
        import psutil
    except ImportError:
        print("❌ 需要安装psutil库: pip install psutil")
        return

    result = experiment3_resource_comparison(5, 20)

if __name__ == "__main__":
    main()