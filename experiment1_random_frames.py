#!/usr/bin/env python3
import json
import os
import random
import time
import cv2
import numpy as np
from typing import List, Tu<PERSON>, Dict

class FrameData:
    def __init__(self, clip_path: str, frame_index: int, video_path: str, jpg_path: str, key_frame_index: int):
        self.clip_path = clip_path
        self.frame_index = frame_index
        self.video_path = video_path
        self.jpg_path = jpg_path
        self.key_frame_index = key_frame_index

def load_frame_data():
    """加载所有帧数据"""
    print("加载clip数据...")
    frame_data_list = []

    with open('local_z10_10.json', 'r') as f:
        paths_data = json.load(f)

    clip_paths = paths_data['paths']

    for clip_path in clip_paths:
        # 读取对应的video json文件
        clip_dir = os.path.dirname(clip_path)
        clip_name = os.path.splitext(os.path.basename(clip_path))[0]
        video_json_path = os.path.join(clip_dir, f"{clip_name}_video.json")

        if not os.path.exists(video_json_path):
            print(f"警告: 找不到video json文件: {video_json_path}")
            continue

        try:
            with open(video_json_path, 'r') as f:
                video_data = json.load(f)

            video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

            for frame in video_data.get('frames', []):
                sensor_data = frame.get('sensor_data', {})
                cam_data = sensor_data.get('cam_front_120', {})

                if 'video_path' in cam_data and 'file_path' in cam_data:
                    frame_data = FrameData(
                        clip_path=clip_path,
                        frame_index=cam_data.get('frame_index', 0),
                        video_path=cam_data['video_path'],
                        jpg_path=cam_data['file_path'],
                        key_frame_index=cam_data.get('key_frame_index', 0)
                    )
                    frame_data_list.append(frame_data)

        except Exception as e:
            print(f"加载clip数据失败: {e}")
            continue

    print(f"总共加载了 {len(frame_data_list)} 帧数据")
    return frame_data_list

def read_jpg_frame(jpg_path: str) -> np.ndarray:
    """从JPG文件读取图片"""
    if not os.path.exists(jpg_path):
        raise Exception(f"JPG文件不存在: {jpg_path}")
    return cv2.imread(jpg_path)

def read_video_frame_from_key(video_path: str, key_frame_index: int, target_frame_index: int):
    """
    从指定关键帧开始解码，直到目标帧。
    适合连续访问或批量读取邻近帧。
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise Exception(f"无法打开视频文件: {video_path}")

    # 定位到关键帧（不是目标帧！）
    cap.set(cv2.CAP_PROP_POS_FRAMES, key_frame_index)

    current_index = key_frame_index
    frame = None

    while current_index <= target_frame_index:
        ret, frame = cap.read()
        if not ret:
            raise Exception(f"无法解码视频帧: {video_path} 第{current_index}帧")
        if current_index == target_frame_index:
            break
        current_index += 1

    cap.release()
    return frame

def experiment1_random_frames(frame_count: int = 100):
    """实验1: 随机读取100帧"""
    print(f"\n=== 实验1: 随机读取{frame_count}帧 ===")

    frame_data_list = load_frame_data()
    if len(frame_data_list) == 0:
        print("没有可用的帧数据")
        return

    # 随机选择frame_count帧
    selected_frames = random.sample(frame_data_list, min(frame_count, len(frame_data_list)))
    print(f"选择了 {len(selected_frames)} 帧进行测试")

    # JPG读取测试
    print("\n🖼️  JPG读取测试...")
    start_time = time.time()
    jpg_results = []
    jpg_errors = 0
    for i, frame_data in enumerate(selected_frames):
        try:
            frame = read_jpg_frame(frame_data.jpg_path)
            jpg_results.append(frame.shape)
            if (i + 1) % 20 == 0:
                print(f"  已读取 {i + 1}/{len(selected_frames)} 帧")
        except Exception as e:
            jpg_errors += 1
            print(f"  JPG读取错误 (帧{i+1}): {e}")
    jpg_time = time.time() - start_time

    print(f"JPG读取完成: {jpg_time:.4f}秒, 成功 {len(jpg_results)} 帧, 失败 {jpg_errors} 帧")

    # 视频读取测试
    print("\n🎬 视频读取测试...")
    start_time = time.time()
    video_results = []
    video_errors = 0
    for i, frame_data in enumerate(selected_frames):
        try:
            frame = read_video_frame_from_key(frame_data.video_path, frame_data.key_frame_index, frame_data.frame_index)
            video_results.append(frame.shape)
            if (i + 1) % 20 == 0:
                print(f"  已读取 {i + 1}/{len(selected_frames)} 帧")
        except Exception as e:
            video_errors += 1
            print(f"  视频读取错误 (帧{i+1}): {e}")
    video_time = time.time() - start_time

    print(f"视频读取完成: {video_time:.4f}秒, 成功 {len(video_results)} 帧, 失败 {video_errors} 帧")

    # 结果分析
    print(f"\n📊 实验1结果:")
    print(f"  总帧数: {len(selected_frames)}")
    print(f"  JPG时间: {jpg_time:.4f}秒")
    print(f"  视频时间: {video_time:.4f}秒")
    print(f"  JPG平均每帧: {(jpg_time/len(selected_frames))*1000:.2f}ms")
    print(f"  视频平均每帧: {(video_time/len(selected_frames))*1000:.2f}ms")

    speedup = jpg_time / video_time if video_time > 0 else 0
    if speedup > 1:
        print(f"  🚀 视频比JPG快 {speedup:.2f} 倍")
    else:
        print(f"  🐌 JPG比视频快 {1/speedup:.2f} 倍")

    # 保存结果
    result = {
        'experiment': 'experiment1_random_frames',
        'frame_count': len(selected_frames),
        'jpg_time': jpg_time,
        'video_time': video_time,
        'jpg_errors': jpg_errors,
        'video_errors': video_errors,
        'jpg_avg_ms': (jpg_time/len(selected_frames))*1000,
        'video_avg_ms': (video_time/len(selected_frames))*1000,
        'speedup': speedup
    }

    with open('experiment1_result.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    print(f"\n结果已保存到: experiment1_result.json")
    return result

if __name__ == "__main__":
    result = experiment1_random_frames(100)