#!/usr/bin/env python3
import os
import cv2
import numpy as np
import glob
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr
import matplotlib.pyplot as plt

def load_image(image_path):
    """加载图片"""
    if not os.path.exists(image_path):
        print(f"图片不存在: {image_path}")
        return None
    return cv2.imread(image_path)

def calculate_ssim(img1, img2):
    """计算SSIM"""
    # 转换为灰度图进行SSIM计算
    gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

    # 计算SSIM
    ssim_value = ssim(gray1, gray2, data_range=255)
    return ssim_value

def calculate_psnr(img1, img2):
    """计算PSNR"""
    # 计算PSNR
    psnr_value = psnr(img1, img2, data_range=255)
    return psnr_value

def calculate_mse(img1, img2):
    """计算MSE"""
    # 计算均方误差
    mse = np.mean((img1 - img2) ** 2)
    return mse

def compare_images(jpg_dir, pyav_dir):
    """对比两个目录中的图片"""
    # 获取所有JPG图片
    jpg_files = glob.glob(os.path.join(jpg_dir, "*.jpg"))
    jpg_files.sort()

    # 获取所有PyAV图片
    pyav_files = glob.glob(os.path.join(pyav_dir, "*_pyav.jpg"))
    pyav_files.sort()

    print(f"找到 {len(jpg_files)} 个JPG文件")
    print(f"找到 {len(pyav_files)} 个PyAV文件")

    # 创建匹配关系
    comparisons = []

    for jpg_file in jpg_files:
        # 获取基础文件名（移除扩展名）
        jpg_basename = os.path.basename(jpg_file)
        base_name = jpg_basename.replace('.jpg', '')

        # 寻找对应的PyAV文件
        pyav_file = None
        for pyav_file in pyav_files:
            pyav_basename = os.path.basename(pyav_file).replace('_pyav.jpg', '')
            if base_name == pyav_basename:
                pyav_file = pyav_file
                break

        if pyav_file:
            comparisons.append((jpg_file, pyav_file, base_name))

    print(f"找到 {len(comparisons)} 对匹配的图片")

    if not comparisons:
        print("没有找到匹配的图片对")
        return None

    # 计算质量指标
    results = []
    ssim_values = []
    psnr_values = []
    mse_values = []

    for i, (jpg_file, pyav_file, base_name) in enumerate(comparisons):
        # 加载图片
        jpg_img = load_image(jpg_file)
        pyav_img = load_image(pyav_file)

        if jpg_img is None or pyav_img is None:
            print(f"跳过 {base_name}，图片加载失败")
            continue

        # 检查图片尺寸
        if jpg_img.shape != pyav_img.shape:
            print(f"{base_name} 图片尺寸不同: JPG={jpg_img.shape}, PyAV={pyav_img.shape}")
            # 调整尺寸以匹配
            pyav_img = cv2.resize(pyav_img, (jpg_img.shape[1], jpg_img.shape[0]))

        # 计算质量指标
        ssim_val = calculate_ssim(jpg_img, pyav_img)
        psnr_val = calculate_psnr(jpg_img, pyav_img)
        mse_val = calculate_mse(jpg_img, pyav_img)

        results.append({
            'name': base_name,
            'ssim': ssim_val,
            'psnr': psnr_val,
            'mse': mse_val,
            'jpg_file': jpg_file,
            'pyav_file': pyav_file
        })

        ssim_values.append(ssim_val)
        psnr_values.append(psnr_val)
        mse_values.append(mse_val)

        if (i + 1) % 10 == 0:
            print(f"  已处理 {i + 1}/{len(comparisons)} 个图片对")

    # 计算统计信息
    if ssim_values:
        ssim_stats = {
            'mean': np.mean(ssim_values),
            'std': np.std(ssim_values),
            'min': np.min(ssim_values),
            'max': np.max(ssim_values)
        }

        psnr_stats = {
            'mean': np.mean(psnr_values),
            'std': np.std(psnr_values),
            'min': np.min(psnr_values),
            'max': np.max(psnr_values)
        }

        mse_stats = {
            'mean': np.mean(mse_values),
            'std': np.std(mse_values),
            'min': np.min(mse_values),
            'max': np.max(mse_values)
        }

        # 打印统计结果
        print(f"\n📊 图片质量对比统计:")
        print(f"  比较图片对数: {len(results)}")
        print(f"  SSIM - 平均: {ssim_stats['mean']:.4f}, 标准差: {ssim_stats['std']:.4f}")
        print(f"         最小: {ssim_stats['min']:.4f}, 最大: {ssim_stats['max']:.4f}")
        print(f"  PSNR - 平均: {psnr_stats['mean']:.2f}dB, 标准差: {psnr_stats['std']:.2f}dB")
        print(f"         最小: {psnr_stats['min']:.2f}dB, 最大: {psnr_stats['max']:.2f}dB")
        print(f"  MSE  - 平均: {mse_stats['mean']:.2f}, 标准差: {mse_stats['std']:.2f}")
        print(f"         最小: {mse_stats['min']:.2f}, 最大: {mse_stats['max']:.2f}")

        # 质量评级
        print(f"\n🎯 质量评级 (SSIM):")
        if ssim_stats['mean'] > 0.95:
            print("  🟢 优秀 (>0.95)")
        elif ssim_stats['mean'] > 0.9:
            print("  🟡 良好 (0.9-0.95)")
        elif ssim_stats['mean'] > 0.8:
            print("  🟡 中等 (0.8-0.9)")
        else:
            print("  🔴 较差 (<0.8)")

        return {
            'comparisons': results,
            'ssim_stats': ssim_stats,
            'psnr_stats': psnr_stats,
            'mse_stats': mse_stats
        }
    else:
        print("没有有效的图片数据进行比较")
        return None

def save_comparison_plots(results, output_dir="quality_comparison"):
    """保存对比结果图表"""
    os.makedirs(output_dir, exist_ok=True)

    # 提取数据
    names = [r['name'] for r in results['comparisons']]
    ssim_values = [r['ssim'] for r in results['comparisons']]
    psnr_values = [r['psnr'] for r in results['comparisons']]
    mse_values = [r['mse'] for r in results['comparisons']]

    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    # SSIM分布直方图
    ax1.hist(ssim_values, bins=20, alpha=0.7, color='blue', edgecolor='black')
    ax1.set_xlabel('SSIM值')
    ax1.set_ylabel('频次')
    ax1.set_title('SSIM值分布')
    ax1.grid(True, alpha=0.3)

    # PSNR分布直方图
    ax2.hist(psnr_values, bins=20, alpha=0.7, color='green', edgecolor='black')
    ax2.set_xlabel('PSNR值 (dB)')
    ax2.set_ylabel('频次')
    ax2.set_title('PSNR值分布')
    ax2.grid(True, alpha=0.3)

    # MSE分布直方图
    ax3.hist(mse_values, bins=20, alpha=0.7, color='red', edgecolor='black')
    ax3.set_xlabel('MSE值')
    ax3.set_ylabel('频次')
    ax3.set_title('MSE值分布')
    ax3.grid(True, alpha=0.3)

    # SSIM vs PSNR散点图
    ax4.scatter(ssim_values, psnr_values, alpha=0.6, c='purple')
    ax4.set_xlabel('SSIM值')
    ax4.set_ylabel('PSNR值 (dB)')
    ax4.set_title('SSIM vs PSNR 相关性')
    ax4.grid(True, alpha=0.3)

    # 添加统计线
    ax4.axhline(y=results['psnr_stats']['mean'], color='red', linestyle='--', alpha=0.7, label=f'平均PSNR: {results["psnr_stats"]["mean"]:.1f}dB')
    ax4.axvline(x=results['ssim_stats']['mean'], color='blue', linestyle='--', alpha=0.7, label=f'平均SSIM: {results["ssim_stats"]["mean"]:.3f}')
    ax4.legend()

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'quality_comparison_plots.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print(f"质量对比图表已保存到: {output_dir}/quality_comparison_plots.png")

def save_detailed_results(results, output_dir="quality_comparison"):
    """保存详细结果到CSV"""
    os.makedirs(output_dir, exist_ok=True)

    import csv
    csv_file = os.path.join(output_dir, 'detailed_quality_results.csv')

    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['图片名称', 'SSIM', 'PSNR(dB)', 'MSE', 'JPG文件', 'PyAV文件'])

        for result in results['comparisons']:
            writer.writerow([
                result['name'],
                f"{result['ssim']:.6f}",
                f"{result['psnr']:.2f}",
                f"{result['mse']:.2f}",
                result['jpg_file'],
                result['pyav_file']
            ])

    print(f"详细结果已保存到: {csv_file}")

def main():
    """主函数"""
    jpg_dir = "/data/workspace/vframe/comparison_images/jpg"
    pyav_dir = "/data/workspace/vframe/comparison_images/pyav"

    print("🔍 开始对比JPG和PyAV图片质量...")
    print(f"JPG目录: {jpg_dir}")
    print(f"PyAV目录: {pyav_dir}")

    if not os.path.exists(jpg_dir):
        print(f"JPG目录不存在: {jpg_dir}")
        return

    if not os.path.exists(pyav_dir):
        print(f"PyAV目录不存在: {pyav_dir}")
        return

    # 执行图片质量对比
    results = compare_images(jpg_dir, pyav_dir)

    if results:
        # 保存图表
        save_comparison_plots(results)

        # 保存详细结果
        save_detailed_results(results)

        print(f"\n✅ 图片质量对比完成!")
        print(f"📊 结果文件:")
        print(f"  - quality_comparison_plots.png (对比图表)")
        print(f"  - detailed_quality_results.csv (详细数据)")

        # 保存汇总结果为JSON
        import json
        summary = {
            'test_name': 'JPG_vs_PyAV_Quality_Comparison',
            'comparison_count': len(results['comparisons']),
            'ssim_mean': results['ssim_stats']['mean'],
            'ssim_std': results['ssim_stats']['std'],
            'psnr_mean': results['psnr_stats']['mean'],
            'psnr_std': results['psnr_stats']['std'],
            'mse_mean': results['mse_stats']['mean'],
            'mse_std': results['mse_stats']['std']
        }

        with open('image_quality_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"  - image_quality_summary.json (汇总结果)")
    else:
        print("❌ 图片质量对比失败")

if __name__ == "__main__":
    main()