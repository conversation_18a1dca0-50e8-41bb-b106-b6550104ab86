#!/usr/bin/env python3
import json
import os
import cv2
import subprocess
import shutil
import time
from pathlib import Path
from tqdm import tqdm

def load_dataset():
    """加载数据集"""
    print("加载数据集...")
    with open('local_z10_100.json', 'r') as f:
        data = json.load(f)

    paths = data.get('paths', [])
    print(f"找到 {len(paths)} 个clip路径")
    return paths

def convert_s3_to_local_path(s3_path, output_dir="jpg"):
    """将s3路径转换为本地路径"""
    if not s3_path.startswith("s3://"):
        return s3_path

    # 提取bucket名称和相对路径
    path_parts = s3_path[5:].split('/', 1)
    if len(path_parts) < 2:
        return s3_path

    bucket_name = path_parts[0]
    relative_path = path_parts[1]

    # 创建本地路径
    local_path = os.path.join("/data/workspace/vframe/jpg", bucket_name, relative_path)
    return local_path

def read_clip_json(clip_json_path):
    """读取clip json文件"""
    try:
        with open(clip_json_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"读取clip json失败: {clip_json_path}, 错误: {e}")
        return None

def create_video_from_images(images, output_video_path, fps=30, crf=20):
    """从图片序列创建H265视频"""
    if not images:
        print("没有图片可处理")
        return False

    # 确保输出目录存在
    output_dir = os.path.dirname(output_video_path)
    os.makedirs(output_dir, exist_ok=True)

    # 使用ffmpeg创建H265视频
    # 创建临时图片列表文件
    temp_list_file = os.path.join(output_dir, "temp_image_list.txt")

    with open(temp_list_file, 'w') as f:
        for img_path in images:
            f.write(f"file '{img_path}'\n")

    # ffmpeg命令 (修复参数格式)
    cmd = [
        'ffmpeg',
        #'-f', 'concat',
        #'-safe', '0',
        '-i', temp_list_file,
        '-c:v', 'libx265',
        #'-preset', 'medium',
        '-crf', str(crf),
        '-pix_fmt', 'yuv420p',
        '-x265-params', 'keyint=10:min-keyint=10:no-scenecut=1',
        '-r', str(fps),
        '-y',
        output_video_path
    ]

    try:
        print(f"开始创建视频: {output_video_path}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)

        if result.returncode == 0:
            print(f"视频创建成功: {output_video_path}")
            return True
        else:
            print(f"ffmpeg错误: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print(f"创建视频超时: {output_video_path}")
        return False
    except Exception as e:
        print(f"创建视频异常: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(temp_list_file):
            os.remove(temp_list_file)

def process_clip(clip_json_path):
    """处理单个clip"""
    print(f"\n🎬 处理clip: {os.path.basename(clip_json_path)}")

    # 读取原始clip数据
    original_data = read_clip_json(clip_json_path)
    if not original_data:
        return False

    frames = original_data.get('frames', [])
    if not frames:
        print("没有帧数据")
        return False

    # 收集图片路径和帧信息
    image_data = []
    valid_frames = []

    print("🔍 检查图片文件...")
    for i, frame in tqdm(enumerate(frames), desc="检查图片", total=len(frames), unit="帧"):
        sensor_data = frame.get('sensor_data', {})
        cam_data = sensor_data.get('cam_front_120', {})

        if 'file_path' in cam_data:
            # 转换s3路径为本地路径
            s3_path = cam_data['file_path']
            local_path = convert_s3_to_local_path(s3_path)

            # 检查文件是否存在
            if os.path.exists(local_path):
                frame_info = {
                    'frame_index': i,
                    's3_path': s3_path,
                    'local_path': local_path,
                    'sensor_data': sensor_data
                }
                image_data.append(frame_info)
                valid_frames.append(frame)
            else:
                print(f"图片文件不存在: {local_path}")

    if not image_data:
        print("没有有效的图片文件")
        return False

    # 限制帧数（300帧以内）
    max_frames = min(300, len(image_data))
    selected_images = image_data[:max_frames]
    selected_frames = valid_frames[:max_frames]

    print(f"找到 {len(image_data)} 张图片，选择前 {len(selected_images)} 张")

    # 生成视频文件路径 (输出到pyav目录)
    clip_dir = os.path.dirname(clip_json_path)
    clip_name = os.path.splitext(os.path.basename(clip_json_path))[0]

    # 创建pyav输出目录
    pyav_dir = os.path.join(clip_dir, "pyav")
    os.makedirs(pyav_dir, exist_ok=True)

    video_filename = f"{clip_name}.mp4"
    video_path = os.path.join(pyav_dir, video_filename)

    # 提取图片路径列表
    image_paths = [img['local_path'] for img in selected_images]

    # 创建视频
    print(f"🎬 开始创建视频 ({len(image_paths)} 帧)...")
    success = create_video_from_images(image_paths, video_path)

    if success:
        print(f"✅ 成功创建视频: {video_path}")

        # 创建新的clip json
        new_clip_json_path = os.path.join(clip_dir, f"{clip_name}_pyav.json")

        # 更新frames数据
        new_frames = []
        for i, frame in enumerate(selected_frames):
            new_frame = frame.copy()
            new_sensor_data = new_frame.get('sensor_data', {})
            new_cam_data = new_sensor_data.get('cam_front_120', {})

            # 添加新字段
            new_cam_data['video_path'] = video_path  # 完整视频文件路径
            new_cam_data['video_timestamp'] = i  # 视频中的帧数

            # 修改file_path为本地路径
            new_cam_data['file_path'] = selected_images[i]['local_path']

            new_sensor_data['cam_front_120'] = new_cam_data
            new_frame['sensor_data'] = new_sensor_data

            new_frames.append(new_frame)

        # 创建新的clip数据
        new_clip_data = {
            'frames': new_frames
        }

        # 保存新的clip json
        try:
            with open(new_clip_json_path, 'w', encoding='utf-8') as f:
                json.dump(new_clip_data, f, indent=2, ensure_ascii=False)
            print(f"✅ 成功创建新clip json: {new_clip_json_path}")
            return True
        except Exception as e:
            print(f"❌ 创建新clip json失败: {e}")
            return False
    else:
        print(f"❌ 创建视频失败: {video_path}")
        return False

def main():
    """主函数"""
    print("🚀 开始转换JPG图片为H265视频")
    start_time = time.time()

    # 加载数据集
    clip_paths = load_dataset()

    if not clip_paths:
        print("没有找到clip路径")
        return

    # 创建输出目录
    output_base = "/data/workspace/vframe/jpg"
    os.makedirs(output_base, exist_ok=True)

    # 统计
    success_count = 0
    total_count = len(clip_paths)

    # 使用tqdm处理每个clip
    print("\n🚀 开始处理所有clips...")
    for clip_json_path in tqdm(clip_paths, desc="处理clips", unit="clip"):
        if process_clip(clip_json_path):
            success_count += 1

    # 最终统计
    end_time = time.time()
    total_time = end_time - start_time

    print(f"\n🎉 转换完成!")
    print(f"📊 总体统计:")
    print(f"  总clip数: {total_count}")
    print(f"  成功处理: {success_count}")
    print(f"  失败处理: {total_count - success_count}")
    print(f"  成功率: {success_count/total_count*100:.1f}%")
    print(f"  总耗时: {total_time:.1f} 秒")
    if success_count > 0:
        avg_time_per_clip = total_time / success_count
        print(f"  平均每个clip耗时: {avg_time_per_clip:.1f} 秒")

if __name__ == "__main__":
    main()