#!/usr/bin/env python3
import json
import os
import sys

def analyze_results():
    """分析所有实验结果"""
    print("=" * 80)
    print("读取性能对比分析 - 所有实验结果")
    print("=" * 80)

    results = {}

    # 读取所有实验结果
    experiment_files = [
        'experiment1_result.json',
        'experiment2_result.json',
        'experiment3_result.json'
    ]

    experiment_names = {
        'experiment1_result.json': '实验1: 随机读取100帧',
        'experiment2_result.json': '实验2: 5帧一组连续帧读取',
        'experiment3_result.json': '实验3: 基于关键帧的连续帧读取'
    }

    for exp_file in experiment_files:
        if os.path.exists(exp_file):
            try:
                with open(exp_file, 'r', encoding='utf-8') as f:
                    results[exp_file] = json.load(f)
                    print(f"✅ 已加载: {exp_file}")
            except Exception as e:
                print(f"❌ 加载失败 {exp_file}: {e}")
        else:
            print(f"⚠️  文件不存在: {exp_file}")

    if not results:
        print("\n没有找到任何实验结果文件！")
        print("请先运行各个实验脚本:")
        print("  python3 experiment1_random_frames.py")
        print("  python3 experiment2_continuous_frames.py")
        print("  python3 experiment3_keyframe_continuous_frames.py")
        return

    # 详细结果展示
    for exp_file, result in results.items():
        print(f"\n" + "="*60)
        print(f"{experiment_names[exp_file]}")
        print("="*60)

        if 'frame_count' in result:
            print(f"📊 读取帧数: {result['frame_count']}")
        if 'groups_count' in result:
            print(f"📊 组数: {result['groups_count']}")
        if 'keyframe_groups_count' in result:
            print(f"📊 关键帧组数: {result['keyframe_groups_count']}")

        print(f"\n🖼️  JPG读取:")
        print(f"   时间: {result['jpg_time']:.4f}秒")
        print(f"   错误数: {result['jpg_errors']}")
        print(f"   平均每帧: {result['jpg_avg_ms']:.2f}ms")

        print(f"\n🎬 视频读取:")
        print(f"   时间: {result['video_time']:.4f}秒")
        print(f"   错误数: {result['video_errors']}")
        print(f"   平均每帧: {result['video_avg_ms']:.2f}ms")

        print(f"\n📈 性能对比:")
        print(f"   加速比: {result['speedup']:.2f}x")
        if result['speedup'] > 1:
            print(f"   🚀 视频比JPG快 {result['speedup']:.2f} 倍")
            improvement = ((result['speedup'] - 1) / result['speedup']) * 100
            print(f"   📊 性能提升: {improvement:.1f}%")
        else:
            print(f"   🐌 JPG比视频快 {1/result['speedup']:.2f} 倍")

    # 综合对比分析
    print(f"\n" + "="*80)
    print("综合对比分析")
    print("="*80)

    print(f"{'实验类型':<25} {'帧数':<8} {'JPG(ms)':<10} {'视频(ms)':<10} {'加速比':<10} {'结论'}")
    print("-" * 80)

    for exp_file, result in results.items():
        exp_name = experiment_names[exp_file].split(':')[-1].strip()
        frame_count = result.get('frame_count', 0)
        jpg_ms = result['jpg_avg_ms']
        video_ms = result['video_avg_ms']
        speedup = result['speedup']

        conclusion = "🚀视频更快" if speedup > 1 else "🐌JPG更快"

        print(f"{exp_name:<25} {frame_count:<8} {jpg_ms:<10.2f} {video_ms:<10.2f} {speedup:<10.2f} {conclusion}")

    # 总结分析
    print(f"\n" + "-"*80)
    print("📊 总结分析")
    print("-"*80)

    # 计算平均加速比
    speedups = [r['speedup'] for r in results.values()]
    avg_speedup = sum(speedups) / len(speedups)

    print(f"平均加速比: {avg_speedup:.2f}x")

    if avg_speedup > 1:
        print(f"🎯 结论: 视频读取比JPG快 {avg_speedup:.2f} 倍")
        print("💡 建议: 对于频繁的帧读取场景，使用压缩视频能显著提升性能")
    else:
        print(f"🎯 结论: JPG读取比视频快 {1/avg_speedup:.2f} 倍")
        print("💡 建议: 对于少量、随机的帧读取，直接使用JPG文件可能更高效")

    # 保存综合分析结果
    summary = {
        'experiments_count': len(results),
        'average_speedup': avg_speedup,
        'recommendation': 'video' if avg_speedup > 1 else 'jpg',
        'detailed_results': results
    }

    with open('read_performance_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)

    print(f"\n📄 综合分析结果已保存到: read_performance_summary.json")

if __name__ == "__main__":
    analyze_results()