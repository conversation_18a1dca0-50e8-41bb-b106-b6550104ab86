#!/usr/bin/env python3
import cv2
import av
import numpy as np
import os

def read_frame_opencv(video_path: str, frame_index: int) -> np.ndarray:
    """使用OpenCV读取指定帧"""
    cap = cv2.VideoCapture(video_path)
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
    ret, frame = cap.read()
    cap.release()
    if not ret:
        raise Exception(f"OpenCV无法读取第{frame_index}帧")
    return frame

def read_frame_pyav(video_path: str, frame_index: int) -> np.ndarray:
    """使用PyAV读取指定帧"""
    container = av.open(video_path)
    stream = container.streams.video[0]

    # 先seek到目标帧附近
    frame_pts = int(frame_index * stream.duration / stream.frames)
    container.seek(frame_pts, stream=stream)

    current_index = 0
    for packet in container.demux(stream):
        for frame in packet.decode():
            if current_index == frame_index:
                img = frame.to_ndarray(format="bgr24")
                container.close()
                return img
            current_index += 1

    container.close()
    raise Exception(f"PyAV无法读取第{frame_index}帧")

def compare_frames(frame1: np.ndarray, frame2: np.ndarray, tolerance: int = 1) -> bool:
    """比较两帧是否在容差范围内相同"""
    if frame1.shape != frame2.shape:
        print(f"形状不同: {frame1.shape} vs {frame2.shape}")
        return False

    # 计算差异
    diff = np.abs(frame1.astype(np.int16) - frame2.astype(np.int16))
    max_diff = np.max(diff)

    return max_diff <= tolerance

def test_data_consistency(video_path: str = None):
    """测试PyAV和OpenCV读取的数据一致性"""

    if video_path is None:
        # 从frame_data中找一个视频文件进行测试
        import json
        with open('local_z10_10.json', 'r') as f:
            paths_data = json.load(f)

        clip_paths = paths_data['paths']
        if not clip_paths:
            print("没有找到可用的clip路径")
            return

        # 找第一个clip的视频文件
        clip_path = clip_paths[0]
        clip_dir = os.path.dirname(clip_path)
        clip_name = os.path.splitext(os.path.basename(clip_path))[0]
        video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

        if not os.path.exists(video_path):
            print(f"视频文件不存在: {video_path}")
            return

    print(f"测试视频文件: {video_path}")

    # 获取视频总帧数
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    cap.release()

    print(f"视频总帧数: {total_frames}")

    # 测试几个不同的帧
    test_indices = [0, total_frames//4, total_frames//2, total_frames*3//4, total_frames-1]

    all_consistent = True

    for frame_index in test_indices:
        if frame_index >= total_frames:
            continue

        print(f"\n测试第 {frame_index} 帧...")

        try:
            # 使用两种方法读取同一帧
            frame_opencv = read_frame_opencv(video_path, frame_index)
            frame_pyav = read_frame_pyav(video_path, frame_index)

            print(f"OpenCV帧形状: {frame_opencv.shape}")
            print(f"PyAV帧形状: {frame_pyav.shape}")
            print(f"OpenCV数据类型: {frame_opencv.dtype}")
            print(f"PyAV数据类型: {frame_pyav.dtype}")

            # 比较数据一致性
            is_consistent = compare_frames(frame_opencv, frame_pyav, tolerance=2)

            if is_consistent:
                print("✅ 数据一致")
            else:
                print("❌ 数据不一致")
                all_consistent = False

                # 显示差异统计
                diff = np.abs(frame_opencv.astype(np.int16) - frame_pyav.astype(np.int16))
                max_diff = np.max(diff)
                mean_diff = np.mean(diff)
                print(f"最大差异: {max_diff}")
                print(f"平均差异: {mean_diff:.2f}")

                # 显示差异的区域
                diff_regions = np.where(diff > 2)
                if len(diff_regions[0]) > 0:
                    num_diff_pixels = len(diff_regions[0])
                    print(f"差异像素数量: {num_diff_pixels}")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            all_consistent = False

    print(f"\n🎯 总体结果:")
    if all_consistent:
        print("✅ 所有测试帧的数据都一致")
    else:
        print("❌ 存在数据不一致的帧")

    return all_consistent

def test_multiple_videos(num_videos: int = 3):
    """测试多个视频文件的数据一致性"""
    print(f"\n=== 测试 {num_videos} 个视频文件的数据一致性 ===")

    import json
    with open('local_z10_10.json', 'r') as f:
        paths_data = json.load(f)

    clip_paths = paths_data['paths']

    tested_count = 0
    consistent_count = 0

    for clip_path in clip_paths[:num_videos]:
        clip_dir = os.path.dirname(clip_path)
        clip_name = os.path.splitext(os.path.basename(clip_path))[0]
        video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

        if os.path.exists(video_path):
            print(f"\n--- 测试视频 {tested_count + 1}: {os.path.basename(video_path)} ---")
            is_consistent = test_data_consistency(video_path)
            tested_count += 1
            if is_consistent:
                consistent_count += 1
        else:
            print(f"视频文件不存在: {video_path}")

    print(f"\n📊 多视频测试结果:")
    print(f"测试视频数: {tested_count}")
    print(f"数据一致视频数: {consistent_count}")
    print(f"一致性比例: {consistent_count/tested_count*100:.1f}%" if tested_count > 0 else "无有效测试")

    return consistent_count == tested_count

if __name__ == "__main__":
    # 测试单个视频
    test_data_consistency()

    # 测试多个视频
    test_multiple_videos(3)