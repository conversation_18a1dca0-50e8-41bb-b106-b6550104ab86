#!/usr/bin/env python3
import json
import os
import random
import time
import cv2
import numpy as np
import av
import psutil
import threading
from typing import List, Tuple, Dict
import matplotlib.pyplot as plt

class ResourceMonitor:
    def __init__(self, interval=0.1):
        self.interval = interval
        self.monitoring = False
        self.cpu_usage = []
        self.memory_usage = []
        self.timestamps = []
        self.thread = None
        self.stop_event = threading.Event()

    def start_monitoring(self):
        """开始监控资源使用情况"""
        # 重置状态
        self.monitoring = True
        self.cpu_usage = []
        self.memory_usage = []
        self.timestamps = []
        self.start_time = time.time()
        self.stop_event.clear()

        # 创建新的监控线程
        self.thread = threading.Thread(target=self._monitor_loop)
        self.thread.daemon = True
        self.thread.start()

    def stop_monitoring(self):
        """停止监控并返回统计数据"""
        # 立即停止监控
        self.monitoring = False
        self.stop_event.set()

        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)  # 等待2秒

        # 清理线程引用
        self.thread = None
        self.stop_event.clear()

        if not self.cpu_usage:
            return None

        return {
            'cpu_avg': np.mean(self.cpu_usage) if self.cpu_usage else 0,
            'cpu_max': np.max(self.cpu_usage) if self.cpu_usage else 0,
            'cpu_min': np.min(self.cpu_usage) if self.cpu_usage else 0,
            'memory_avg': np.mean(self.memory_usage) if self.memory_usage else 0,
            'memory_max': np.max(self.memory_usage) if self.memory_usage else 0,
            'memory_min': np.min(self.memory_usage) if self.memory_usage else 0,
            'cpu_usage': self.cpu_usage,
            'memory_usage': self.memory_usage,
            'timestamps': self.timestamps,
            'duration': len(self.timestamps) * self.interval if self.timestamps else 0
        }

    def _monitor_loop(self):
        """监控循环"""
        process = psutil.Process()

        while self.monitoring and not self.stop_event.is_set():
            try:
                # 单次CPU采样，避免累积
                cpu_percent = process.cpu_percent(interval=None)
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024  # 转换为MB

                # 限制CPU使用率合理范围
                cpu_percent = min(cpu_percent, 100.0)

                self.cpu_usage.append(cpu_percent)
                self.memory_usage.append(memory_mb)
                self.timestamps.append(time.time() - self.start_time)

                # 间隔睡眠，避免过度占用CPU
                time.sleep(self.interval)

            except Exception as e:
                print(f"资源监控错误: {e}")
                break

class FrameData:
    def __init__(self, clip_path: str, frame_index: int, video_path: str, jpg_path: str, key_frame_index: int):
        self.clip_path = clip_path
        self.frame_index = frame_index
        self.video_path = video_path
        self.jpg_path = jpg_path
        self.key_frame_index = key_frame_index

def load_frame_data():
    """加载所有帧数据"""
    print("加载clip数据...")
    frame_data_list = []

    with open('local_z10_10.json', 'r') as f:
        paths_data = json.load(f)

    clip_paths = paths_data['paths']

    for clip_path in clip_paths:
        # 读取对应的video json文件
        clip_dir = os.path.dirname(clip_path)
        clip_name = os.path.splitext(os.path.basename(clip_path))[0]
        video_json_path = os.path.join(clip_dir, f"{clip_name}_video.json")

        if not os.path.exists(video_json_path):
            print(f"警告: 找不到video json文件: {video_json_path}")
            continue

        try:
            with open(video_json_path, 'r') as f:
                video_data = json.load(f)

            video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

            for frame in video_data.get('frames', []):
                sensor_data = frame.get('sensor_data', {})
                cam_data = sensor_data.get('cam_front_120', {})

                if 'video_path' in cam_data and 'file_path' in cam_data:
                    frame_data = FrameData(
                        clip_path=clip_path,
                        frame_index=cam_data.get('frame_index', 0),
                        video_path=cam_data['video_path'],
                        jpg_path=cam_data['file_path'],
                        key_frame_index=cam_data.get('key_frame_index', 0)
                    )
                    frame_data_list.append(frame_data)

        except Exception as e:
            print(f"加载clip数据失败: {e}")
            continue

    print(f"总共加载了 {len(frame_data_list)} 帧数据")
    return frame_data_list

def read_jpg_frame(jpg_path: str) -> np.ndarray:
    """从JPG文件读取图片"""
    if not os.path.exists(jpg_path):
        raise Exception(f"JPG文件不存在: {jpg_path}")
    return cv2.imread(jpg_path)

def read_continuous_frames_from_key_pyav(video_path: str, key_frame_index: int, target_frame_index: int, num_frames: int = 5):
    """
    使用 PyAV 从指定关键帧开始解码，读取从目标帧起连续的 num_frames 帧。
    适合大规模数据训练场景，可精确控制关键帧访问与解码范围。
    """
    container = None
    try:
        container = av.open(video_path)

        # 选择视频流
        stream = container.streams.video[0]

        # 修正时间戳计算
        seek_timestamp = int(key_frame_index / stream.average_rate * stream.time_base.denominator / stream.time_base.numerator)

        # 定位到关键帧附近
        container.seek(seek_timestamp, stream=stream)

        frames = []
        current_index = key_frame_index  # 假设从关键帧开始
        found_target = False
        decoded_count = 0  # 实际解码的帧计数

        for packet in container.demux(stream):
            for frame in packet.decode():
                decoded_count += 1

                # 当解码到目标帧时开始收集
                if current_index >= target_frame_index:
                    found_target = True
                    img = frame.to_ndarray(format="bgr24")
                    frames.append(img)

                if found_target and len(frames) >= num_frames:
                    break

                current_index += 1
            if found_target and len(frames) >= num_frames:
                break

        if not found_target:
            raise Exception(f"未找到目标帧: {video_path} 第{target_frame_index}帧 (seek到: {seek_timestamp}, 实际解码: {decoded_count}帧)")

        return frames

    return frames

def save_resource_plots(jpg_stats, pyav_stats, save_dir="resource_plots"):
    """保存资源使用对比图"""
    os.makedirs(save_dir, exist_ok=True)

    # CPU使用率对比
    plt.figure(figsize=(12, 8))

    plt.subplot(2, 2, 1)
    if jpg_stats and jpg_stats['cpu_usage']:
        plt.plot(jpg_stats['timestamps'], jpg_stats['cpu_usage'], 'b-', label='JPG', alpha=0.7)
    if pyav_stats and pyav_stats['cpu_usage']:
        plt.plot(pyav_stats['timestamps'], pyav_stats['cpu_usage'], 'r-', label='PyAV', alpha=0.7)
    plt.xlabel('时间 (秒)')
    plt.ylabel('CPU使用率 (%)')
    plt.title('CPU使用率对比')
    plt.legend()
    plt.grid(True)

    # 内存使用对比
    plt.subplot(2, 2, 2)
    if jpg_stats and jpg_stats['memory_usage']:
        plt.plot(jpg_stats['timestamps'], jpg_stats['memory_usage'], 'b-', label='JPG', alpha=0.7)
    if pyav_stats and pyav_stats['memory_usage']:
        plt.plot(pyav_stats['timestamps'], pyav_stats['memory_usage'], 'r-', label='PyAV', alpha=0.7)
    plt.xlabel('时间 (秒)')
    plt.ylabel('内存使用 (MB)')
    plt.title('内存使用对比')
    plt.legend()
    plt.grid(True)

    # 平均资源使用对比
    plt.subplot(2, 2, 3)
    methods = ['JPG', 'PyAV']
    cpu_avgs = []
    memory_avgs = []

    if jpg_stats:
        cpu_avgs.append(jpg_stats['cpu_avg'])
        memory_avgs.append(jpg_stats['memory_avg'])
    else:
        cpu_avgs.append(0)
        memory_avgs.append(0)

    if pyav_stats:
        cpu_avgs.append(pyav_stats['cpu_avg'])
        memory_avgs.append(pyav_stats['memory_avg'])
    else:
        cpu_avgs.append(0)
        memory_avgs.append(0)

    x = np.arange(len(methods))
    width = 0.35

    plt.bar(x - width/2, cpu_avgs, width, label='平均CPU', alpha=0.7)
    plt.bar(x + width/2, memory_avgs, width, label='平均内存', alpha=0.7)
    plt.xlabel('方法')
    plt.ylabel('使用量')
    plt.title('平均资源使用对比')
    plt.xticks(x, methods)
    plt.legend()
    plt.grid(True)

    # 资源效率对比
    plt.subplot(2, 2, 4)
    efficiency_data = []
    efficiency_labels = []

    if jpg_stats:
        # JPG效率：CPU使用率越低越好，内存使用越低越好
        jpg_efficiency = 100 / (1 + jpg_stats['cpu_avg']/10 + jpg_stats['memory_avg']/100)
        efficiency_data.append(jpg_efficiency)
        efficiency_labels.append('JPG')

    if pyav_stats:
        # PyAV效率
        pyav_efficiency = 100 / (1 + pyav_stats['cpu_avg']/10 + pyav_stats['memory_avg']/100)
        efficiency_data.append(pyav_efficiency)
        efficiency_labels.append('PyAV')

    if efficiency_data:
        plt.bar(efficiency_labels, efficiency_data, alpha=0.7)
        plt.ylabel('效率分数')
        plt.title('资源效率对比 (越高越好)')
        plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'resource_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print(f"资源对比图已保存到: {save_dir}/resource_comparison.png")

def experiment4_resource_monitor(group_size: int = 5, total_groups: int = 20, save_images: bool = False, test_mode: str = "both"):
    """实验4: 资源监控版本 - PyAV vs JPG 关键帧连续帧读取性能与资源消耗对比"""
    print(f"\n=== 实验4(资源监控): PyAV vs JPG 关键帧连续帧读取性能与资源消耗对比 ({group_size}帧一组, {total_groups}组) ===")

    frame_data_list = load_frame_data()
    if len(frame_data_list) == 0:
        print("没有可用的帧数据")
        return

    # 按clip分组，找出关键帧
    keyframes_by_clip = {}
    for frame_data in frame_data_list:
        clip_key = frame_data.clip_path
        if clip_key not in keyframes_by_clip:
            keyframes_by_clip[clip_key] = []

        # 如果当前帧是关键帧
        if frame_data.frame_index == frame_data.key_frame_index:
            keyframes_by_clip[clip_key].append(frame_data)

    # 为每个clip的关键帧排序
    for clip_key in keyframes_by_clip:
        keyframes_by_clip[clip_key].sort(key=lambda x: x.frame_index)

    # 收集所有关键帧
    all_keyframes = []
    for clip_key, keyframes in keyframes_by_clip.items():
        all_keyframes.extend(keyframes)

    print(f"找到 {len(all_keyframes)} 个关键帧")

    # 随机选择total_groups个关键帧
    if len(all_keyframes) < total_groups:
        print(f"关键帧不足，只有 {len(all_keyframes)} 个，无法选择 {total_groups} 组")
        return

    selected_keyframes = random.sample(all_keyframes, total_groups)

    # 为每个选中的关键帧构建连续帧组
    selected_groups = []
    selected_frames = []

    for keyframe in selected_keyframes:
        # 获取该关键帧所在clip的所有帧
        clip_key = keyframe.clip_path
        clip_frames = [f for f in frame_data_list if f.clip_path == clip_key]
        clip_frames.sort(key=lambda x: x.frame_index)

        # 找到关键帧在clip_frames中的位置
        keyframe_pos = None
        for i, f in enumerate(clip_frames):
            if f.frame_index == keyframe.frame_index:
                keyframe_pos = i
                break

        if keyframe_pos is not None:
            # 从关键帧位置开始取group_size帧
            if keyframe_pos + group_size <= len(clip_frames):
                group = clip_frames[keyframe_pos:keyframe_pos + group_size]
                selected_groups.append((clip_key, keyframe.frame_index, group))
                selected_frames.extend(group)
            else:
                print(f"关键帧 {keyframe.frame_index} 位置 {keyframe_pos}，无法组成 {group_size} 帧组")
        else:
            print(f"未找到关键帧 {keyframe.frame_index}")

    print(f"选择了 {len(selected_groups)} 个基于关键帧的连续帧组，共 {len(selected_frames)} 帧")

    # 创建保存目录
    if save_images:
        jpg_dir = "comparison_images_resources/jpg"
        pyav_dir = "comparison_images_resources/pyav"
        os.makedirs(jpg_dir, exist_ok=True)
        os.makedirs(pyav_dir, exist_ok=True)

    jpg_time = 0
    pyav_time = 0
    jpg_stats = None
    pyav_stats = None
    jpg_results = []
    pyav_results = []
    jpg_errors = 0
    pyav_errors = 0

    # 根据测试模式执行不同测试
    if test_mode in ["jpg", "both"]:
        # JPG独立测试
        print("\n🖼️  JPG读取测试（基准）...")

        jpg_frames_copy = selected_frames.copy()

        print("\n📊 启动JPG资源监控...")
        monitor_jpg = ResourceMonitor(interval=0.1)
        monitor_jpg.start_monitoring()

        start_time = time.time()
        jpg_results = []
        jpg_errors = 0
        for i, frame_data in enumerate(jpg_frames_copy):
            try:
                frame = read_jpg_frame(frame_data.jpg_path)
                jpg_results.append(frame.shape)

                # 保存图片
                if save_images:
                    clip_name = os.path.basename(frame_data.clip_path).replace('.json', '')
                    filename = f"{clip_name}_frame{frame_data.frame_index}.jpg"
                    save_path = os.path.join(jpg_dir, filename)
                    cv2.imwrite(save_path, frame)

                if (i + 1) % 25 == 0:
                    print(f"  JPG已读取 {i + 1}/{len(jpg_frames_copy)} 帧")
            except Exception as e:
                jpg_errors += 1
                print(f"  JPG读取错误 (帧{i+1}): {e}")
        jpg_time = time.time() - start_time

        jpg_stats = monitor_jpg.stop_monitoring()

        print(f"JPG读取完成: {jpg_time:.4f}秒, 成功 {len(jpg_results)} 帧, 失败 {jpg_errors} 帧")
        if jpg_stats:
            print(f"  📈 JPG资源统计:")
            print(f"    CPU平均: {jpg_stats['cpu_avg']:.1f}%, 最大: {jpg_stats['cpu_max']:.1f}%")
            print(f"    内存平均: {jpg_stats['memory_avg']:.1f}MB, 最大: {jpg_stats['memory_max']:.1f}MB")
            print(f"    监控时长: {jpg_stats['duration']:.1f}秒")
        if save_images:
            print(f"JPG图片已保存到: {jpg_dir}")

    # 如果只测试JPG，直接返回结果
    if test_mode == "jpg":
        result = {
            'experiment': 'experiment4_resource_monitor',
            'test_mode': 'jpg_only',
            'keyframe_groups_count': len(selected_groups),
            'frame_count': len(selected_frames),
            'jpg': {
                'time': jpg_time,
                'errors': jpg_errors,
                'avg_ms': (jpg_time/len(selected_frames))*1000 if len(selected_frames) > 0 else 0,
                'resource_stats': jpg_stats
            }
        }

        with open('experiment4_jpg_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"\nJPG测试结果已保存到: experiment4_jpg_result.json")
        return result

    # 等待一段时间让系统资源稳定
    if test_mode == "both":
        print("\n⏳ 等待系统资源稳定 (3秒)...")
        time.sleep(3)

    # PyAV测试
    if test_mode in ["pyav", "both"]:
        print("\n🚀 PyAV读取测试...")

        pyav_frames_copy = selected_frames.copy()

        print("\n📊 启动PyAV资源监控...")
        monitor_pyav = ResourceMonitor(interval=0.1)
        monitor_pyav.start_monitoring()

    start_time = time.time()
    pyav_results = []
    pyav_errors = 0

    # 按(视频路径, 关键帧)分组进行批量读取
    frame_groups = {}
    for i, frame_data in enumerate(pyav_frames_copy):
        group_key = (frame_data.video_path, frame_data.key_frame_index)
        if group_key not in frame_groups:
            frame_groups[group_key] = []
        frame_groups[group_key].append((i, frame_data))

    for (video_path, key_frame_index), group_items in frame_groups.items():
        # 找到最小的目标帧作为起始点
        min_target_frame = min(item[1].frame_index for item in group_items)

        try:
            # 批量读取从最小目标帧开始的连续帧
            frames_batch = read_continuous_frames_from_key_pyav(
                video_path, key_frame_index, min_target_frame, group_size
            )

            # 将读取的帧分配给对应的索引
            for idx, (result_idx, frame_data) in enumerate(group_items):
                relative_index = frame_data.frame_index - min_target_frame
                if relative_index < len(frames_batch):
                    frame = frames_batch[relative_index]
                    pyav_results.append(frame.shape)

                    # 保存PyAV读取的图片
                    if save_images:
                        clip_name = os.path.basename(frame_data.clip_path).replace('.json', '')
                        filename = f"{clip_name}_frame{frame_data.frame_index}_pyav.jpg"
                        save_path = os.path.join(pyav_dir, filename)
                        cv2.imwrite(save_path, frame)
                else:
                    pyav_results.append(None)
                    pyav_errors += 1

        except Exception as e:
            # 如果批量读取失败，记录错误
            for i, (result_idx, frame_data) in enumerate(group_items):
                pyav_results.append(None)
                pyav_errors += 1
            print(f"  PyAV批量读取失败组 {video_path}:{key_frame_index}: {e}")

        start_time = time.time()
        pyav_results = []
        pyav_errors = 0

        # 按(视频路径, 关键帧)分组进行批量读取
        frame_groups = {}
        for i, frame_data in enumerate(pyav_frames_copy):
            group_key = (frame_data.video_path, frame_data.key_frame_index)
            if group_key not in frame_groups:
                frame_groups[group_key] = []
            frame_groups[group_key].append((i, frame_data))

        for (video_path, key_frame_index), group_items in frame_groups.items():
            # 找到最小的目标帧作为起始点
            min_target_frame = min(item[1].frame_index for item in group_items)

            try:
                # 批量读取从最小目标帧开始的连续帧
                frames_batch = read_continuous_frames_from_key_pyav(
                    video_path, key_frame_index, min_target_frame, group_size
                )

                # 将读取的帧分配给对应的索引
                for idx, (result_idx, frame_data) in enumerate(group_items):
                    relative_index = frame_data.frame_index - min_target_frame
                    if relative_index < len(frames_batch):
                        frame = frames_batch[relative_index]
                        pyav_results.append(frame.shape)

                        # 保存PyAV读取的图片
                        if save_images:
                            clip_name = os.path.basename(frame_data.clip_path).replace('.json', '')
                            filename = f"{clip_name}_frame{frame_data.frame_index}_pyav.jpg"
                            save_path = os.path.join(pyav_dir, filename)
                            cv2.imwrite(save_path, frame)
                    else:
                        pyav_results.append(None)
                        pyav_errors += 1

            except Exception as e:
                # 如果批量读取失败，记录错误
                for i, (result_idx, frame_data) in enumerate(group_items):
                    pyav_results.append(None)
                    pyav_errors += 1
                print(f"  PyAV批量读取失败组 {video_path}:{key_frame_index}: {e}")

        # 确保pyav_results长度正确
        while len(pyav_results) < len(pyav_frames_copy):
            pyav_results.append(None)
            pyav_errors += 1

        pyav_time = time.time() - start_time

        pyav_stats = monitor_pyav.stop_monitoring()

        print(f"PyAV读取完成: {pyav_time:.4f}秒, 成功 {len(pyav_results)} 帧, 失败 {pyav_errors} 帧")
        if pyav_stats:
            print(f"  📈 PyAV资源统计:")
            print(f"    CPU平均: {pyav_stats['cpu_avg']:.1f}%, 最大: {pyav_stats['cpu_max']:.1f}%")
            print(f"    内存平均: {pyav_stats['memory_avg']:.1f}MB, 最大: {pyav_stats['memory_max']:.1f}MB")
            print(f"    监控时长: {pyav_stats['duration']:.1f}秒")
        if save_images:
            print(f"PyAV图片已保存到: {pyav_dir}")

    # 如果只测试PyAV，直接返回结果
    if test_mode == "pyav":
        result = {
            'experiment': 'experiment4_resource_monitor',
            'test_mode': 'pyav_only',
            'keyframe_groups_count': len(selected_groups),
            'frame_count': len(selected_frames),
            'pyav': {
                'time': pyav_time,
                'errors': pyav_errors,
                'avg_ms': (pyav_time/len(selected_frames))*1000 if len(selected_frames) > 0 else 0,
                'resource_stats': pyav_stats
            }
        }

        with open('experiment4_pyav_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"\nPyAV测试结果已保存到: experiment4_pyav_result.json")
        return result

    # 结果分析
    print(f"\n📊 实验4(资源监控)结果:")
    print(f"  关键帧组数: {len(selected_groups)}")
    print(f"  总帧数: {len(selected_frames)}")
    print(f"  JPG时间: {jpg_time:.4f}秒")
    print(f"  PyAV时间: {pyav_time:.4f}秒")
    print(f"  JPG平均每帧: {(jpg_time/len(selected_frames))*1000:.2f}ms")
    print(f"  PyAV平均每帧: {(pyav_time/len(selected_frames))*1000:.2f}ms")

    # 计算加速比
    pyav_speedup = jpg_time / pyav_time if pyav_time > 0 else 0

    print(f"\n🏆 性能对比:")
    if pyav_speedup > 1:
        print(f"  🚀 PyAV比JPG快 {pyav_speedup:.2f} 倍")
    else:
        print(f"  🐌 JPG比PyAV快 {1/pyav_speedup:.2f} 倍")

    print(f"\n📈 资源消耗对比:")
    if jpg_stats and pyav_stats:
        cpu_ratio = pyav_stats['cpu_avg'] / jpg_stats['cpu_avg'] if jpg_stats['cpu_avg'] > 0 else 0
        memory_ratio = pyav_stats['memory_avg'] / jpg_stats['memory_avg'] if jpg_stats['memory_avg'] > 0 else 0

        print(f"  CPU使用率比率: PyAV/JPG = {cpu_ratio:.2f}x")
        print(f"  内存使用比率: PyAV/JPG = {memory_ratio:.2f}x")

        if cpu_ratio < 1:
            print(f"  ✅ PyAV CPU使用更低 ({(1-cpu_ratio)*100:.1f}%)")
        else:
            print(f"  ⚠️ PyAV CPU使用更高 ({(cpu_ratio-1)*100:.1f}%)")

        if memory_ratio < 1:
            print(f"  ✅ PyAV内存使用更低 ({(1-memory_ratio)*100:.1f}%)")
        else:
            print(f"  ⚠️ PyAV内存使用更高 ({(memory_ratio-1)*100:.1f}%)")

    # 生成资源对比图
    if jpg_stats and pyav_stats:
        save_resource_plots(jpg_stats, pyav_stats)

    # 保存结果
    result = {
        'experiment': 'experiment4_resource_monitor',
        'keyframe_groups_count': len(selected_groups),
        'frame_count': len(selected_frames),
        'jpg': {
            'time': jpg_time,
            'errors': jpg_errors,
            'avg_ms': (jpg_time/len(selected_frames))*1000,
            'resource_stats': jpg_stats
        },
        'pyav': {
            'time': pyav_time,
            'errors': pyav_errors,
            'avg_ms': (pyav_time/len(selected_frames))*1000,
            'resource_stats': pyav_stats
        },
        'pyav_speedup': pyav_speedup
    }

    with open('experiment4_resource_result.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    print(f"\n结果已保存到: experiment4_resource_result.json")
    return result

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='PyAV vs JPG 性能与资源消耗对比实验')
    parser.add_argument('--group-size', type=int, default=5, help='每组帧数')
    parser.add_argument('--total-groups', type=int, default=20, help='总组数')
    parser.add_argument('--save-images', action='store_true', help='保存对比图片到本地')
    parser.add_argument('--mode', choices=['jpg', 'pyav', 'both'], default='both',
                       help='测试模式: jpg(只测试JPG), pyav(只测试PyAV), both(测试两者)')

    args = parser.parse_args()

    result = experiment4_resource_monitor(args.group_size, args.total_groups, args.save_images, args.mode)