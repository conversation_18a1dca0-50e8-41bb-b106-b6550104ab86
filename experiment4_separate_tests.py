#!/usr/bin/env python3
import json
import os
import random
import time
import cv2
import numpy as np
import av
from typing import List, Tu<PERSON>, Dict

class FrameData:
    def __init__(self, clip_path: str, frame_index: int, video_path: str, jpg_path: str, key_frame_index: int):
        self.clip_path = clip_path
        self.frame_index = frame_index
        self.video_path = video_path
        self.jpg_path = jpg_path
        self.key_frame_index = key_frame_index

def load_frame_data():
    """加载所有帧数据"""
    print("加载clip数据...")
    frame_data_list = []

    with open('local_z10_10.json', 'r') as f:
        paths_data = json.load(f)

    clip_paths = paths_data['paths']

    for clip_path in clip_paths:
        # 读取对应的video json文件
        clip_dir = os.path.dirname(clip_path)
        clip_name = os.path.splitext(os.path.basename(clip_path))[0]
        video_json_path = os.path.join(clip_dir, f"{clip_name}_video.json")

        if not os.path.exists(video_json_path):
            print(f"警告: 找不到video json文件: {video_json_path}")
            continue

        try:
            with open(video_json_path, 'r') as f:
                video_data = json.load(f)

            video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

            for frame in video_data.get('frames', []):
                sensor_data = frame.get('sensor_data', {})
                cam_data = sensor_data.get('cam_front_120', {})

                if 'video_path' in cam_data and 'file_path' in cam_data:
                    frame_data = FrameData(
                        clip_path=clip_path,
                        frame_index=cam_data.get('frame_index', 0),
                        video_path=cam_data['video_path'],
                        jpg_path=cam_data['file_path'],
                        key_frame_index=cam_data.get('key_frame_index', 0)
                    )
                    frame_data_list.append(frame_data)

        except Exception as e:
            print(f"加载clip数据失败: {e}")
            continue

    print(f"总共加载了 {len(frame_data_list)} 帧数据")
    return frame_data_list

def read_jpg_frame(jpg_path: str) -> np.ndarray:
    """从JPG文件读取图片"""
    if not os.path.exists(jpg_path):
        raise Exception(f"JPG文件不存在: {jpg_path}")
    return cv2.imread(jpg_path)

def read_continuous_frames_from_key_pyav(video_path: str, key_frame_index: int, target_frame_index: int, num_frames: int = 5):
    """
    使用 PyAV 从指定关键帧开始解码，读取从目标帧起连续的 num_frames 帧。
    适合大规模数据训练场景，可精确控制关键帧访问与解码范围。
    """
    container = None
    try:
        container = av.open(video_path)

        # 选择视频流
        stream = container.streams.video[0]

        # 修正时间戳计算
        seek_timestamp = int(key_frame_index / stream.average_rate * stream.time_base.denominator / stream.time_base.numerator)

        # 定位到关键帧附近
        container.seek(seek_timestamp, stream=stream)

        frames = []
        current_index = key_frame_index  # 假设从关键帧开始
        found_target = False
        decoded_count = 0  # 实际解码的帧计数

        for packet in container.demux(stream):
            for frame in packet.decode():
                decoded_count += 1

                # 当解码到目标帧时开始收集
                if current_index >= target_frame_index:
                    found_target = True
                    img = frame.to_ndarray(format="bgr24")
                    frames.append(img)

                if found_target and len(frames) >= num_frames:
                    break

                current_index += 1
            if found_target and len(frames) >= num_frames:
                break

        if not found_target:
            raise Exception(f"未找到目标帧: {video_path} 第{target_frame_index}帧 (seek到: {seek_timestamp}, 实际解码: {decoded_count}帧)")

        return frames

    finally:
        # 确保容器正确关闭
        if container is not None:
            try:
                container.close()
            except:
                pass  # 忽略关闭时的异常

def select_test_frames(frame_data_list: list, group_size: int, total_groups: int):
    """选择测试帧，返回selected_groups和selected_frames"""
    # 按clip分组，找出关键帧
    keyframes_by_clip = {}
    for frame_data in frame_data_list:
        clip_key = frame_data.clip_path
        if clip_key not in keyframes_by_clip:
            keyframes_by_clip[clip_key] = []

        # 如果当前帧是关键帧
        if frame_data.frame_index == frame_data.key_frame_index:
            keyframes_by_clip[clip_key].append(frame_data)

    # 为每个clip的关键帧排序
    for clip_key in keyframes_by_clip:
        keyframes_by_clip[clip_key].sort(key=lambda x: x.frame_index)

    # 收集所有关键帧
    all_keyframes = []
    for clip_key, keyframes in keyframes_by_clip.items():
        all_keyframes.extend(keyframes)

    print(f"找到 {len(all_keyframes)} 个关键帧")

    # 随机选择total_groups个关键帧
    if len(all_keyframes) < total_groups:
        print(f"关键帧不足，只有 {len(all_keyframes)} 个，无法选择 {total_groups} 组")
        return [], []

    selected_keyframes = random.sample(all_keyframes, total_groups)

    # 为每个选中的关键帧构建连续帧组
    selected_groups = []
    selected_frames = []

    for keyframe in selected_keyframes:
        # 获取该关键帧所在clip的所有帧
        clip_key = keyframe.clip_path
        clip_frames = [f for f in frame_data_list if f.clip_path == clip_key]
        clip_frames.sort(key=lambda x: x.frame_index)

        # 找到关键帧在clip_frames中的位置
        keyframe_pos = None
        for i, f in enumerate(clip_frames):
            if f.frame_index == keyframe.frame_index:
                keyframe_pos = i
                break

        if keyframe_pos is not None:
            # 从关键帧位置开始取group_size帧
            if keyframe_pos + group_size <= len(clip_frames):
                group = clip_frames[keyframe_pos:keyframe_pos + group_size]
                selected_groups.append((clip_key, keyframe.frame_index, group))
                selected_frames.extend(group)
            else:
                print(f"关键帧 {keyframe.frame_index} 位置 {keyframe_pos}，无法组成 {group_size} 帧组")
        else:
            print(f"未找到关键帧 {keyframe.frame_index}")

    print(f"选择了 {len(selected_groups)} 个基于关键帧的连续帧组，共 {len(selected_frames)} 帧")
    return selected_groups, selected_frames

def run_jpg_test(group_size: int, total_groups: int, save_images: bool):
    """运行JPG测试"""
    print(f"\n🖼️  === JPG独立测试 ({group_size}帧一组, {total_groups}组) ===")

    frame_data_list = load_frame_data()
    if len(frame_data_list) == 0:
        print("没有可用的帧数据")
        return None

    selected_groups, selected_frames = select_test_frames(frame_data_list, group_size, total_groups)
    if not selected_groups:
        return None

    # 创建保存目录
    if save_images:
        jpg_dir = "separate_tests/jpg"
        os.makedirs(jpg_dir, exist_ok=True)

    # JPG读取测试
    start_time = time.time()
    jpg_results = []
    jpg_errors = 0
    for i, frame_data in enumerate(selected_frames):
        try:
            frame = read_jpg_frame(frame_data.jpg_path)
            jpg_results.append(frame.shape)

            # 保存图片
            if save_images:
                clip_name = os.path.basename(frame_data.clip_path).replace('.json', '')
                filename = f"{clip_name}_frame{frame_data.frame_index}.jpg"
                save_path = os.path.join(jpg_dir, filename)
                cv2.imwrite(save_path, frame)

            if (i + 1) % 25 == 0:
                print(f"  已读取 {i + 1}/{len(selected_frames)} 帧")
        except Exception as e:
            jpg_errors += 1
            print(f"  JPG读取错误 (帧{i+1}): {e}")
    jpg_time = time.time() - start_time

    print(f"JPG测试完成: {jpg_time:.4f}秒, 成功 {len(jpg_results)} 帧, 失败 {jpg_errors} 帧")
    print(f"  JPG平均每帧: {(jpg_time/len(selected_frames))*1000:.2f}ms")
    if save_images:
        print(f"  JPG图片已保存到: {jpg_dir}")

    result = {
        'test_mode': 'jpg',
        'group_size': group_size,
        'total_groups': total_groups,
        'selected_groups_count': len(selected_groups),
        'frame_count': len(selected_frames),
        'time': jpg_time,
        'errors': jpg_errors,
        'avg_ms': (jpg_time/len(selected_frames))*1000 if len(selected_frames) > 0 else 0
    }

    with open('experiment4_separate_jpg_result.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    print(f"JPG测试结果已保存到: experiment4_separate_jpg_result.json")
    return result

def run_pyav_test(group_size: int, total_groups: int, save_images: bool):
    """运行PyAV测试"""
    print(f"\n🚀  === PyAV独立测试 ({group_size}帧一组, {total_groups}组) ===")

    frame_data_list = load_frame_data()
    if len(frame_data_list) == 0:
        print("没有可用的帧数据")
        return None

    selected_groups, selected_frames = select_test_frames(frame_data_list, group_size, total_groups)
    if not selected_groups:
        return None

    # 创建保存目录
    if save_images:
        pyav_dir = "separate_tests/pyav"
        os.makedirs(pyav_dir, exist_ok=True)

    # PyAV读取测试
    start_time = time.time()
    pyav_results = []
    pyav_errors = 0

    # 按(视频路径, 关键帧)分组进行批量读取
    frame_groups = {}
    for i, frame_data in enumerate(selected_frames):
        group_key = (frame_data.video_path, frame_data.key_frame_index)
        if group_key not in frame_groups:
            frame_groups[group_key] = []
        frame_groups[group_key].append((i, frame_data))

    for (video_path, key_frame_index), group_items in frame_groups.items():
        # 找到最小的目标帧作为起始点
        min_target_frame = min(item[1].frame_index for item in group_items)

        try:
            # 批量读取从最小目标帧开始的连续帧
            frames_batch = read_continuous_frames_from_key_pyav(
                video_path, key_frame_index, min_target_frame, group_size
            )

            # 将读取的帧分配给对应的索引
            for idx, (result_idx, frame_data) in enumerate(group_items):
                relative_index = frame_data.frame_index - min_target_frame
                if relative_index < len(frames_batch):
                    frame = frames_batch[relative_index]
                    pyav_results.append(frame.shape)

                    # 保存PyAV读取的图片
                    if save_images:
                        clip_name = os.path.basename(frame_data.clip_path).replace('.json', '')
                        filename = f"{clip_name}_frame{frame_data.frame_index}_pyav.jpg"
                        save_path = os.path.join(pyav_dir, filename)
                        cv2.imwrite(save_path, frame)
                else:
                    pyav_results.append(None)
                    pyav_errors += 1

        except Exception as e:
            # 如果批量读取失败，记录错误
            for i, (result_idx, frame_data) in enumerate(group_items):
                pyav_results.append(None)
                pyav_errors += 1
            print(f"  PyAV批量读取失败组 {video_path}:{key_frame_index}: {e}")

    # 确保pyav_results长度正确
    while len(pyav_results) < len(selected_frames):
        pyav_results.append(None)
        pyav_errors += 1

    pyav_time = time.time() - start_time

    print(f"PyAV测试完成: {pyav_time:.4f}秒, 成功 {len(pyav_results)} 帧, 失败 {pyav_errors} 帧")
    print(f"  PyAV平均每帧: {(pyav_time/len(selected_frames))*1000:.2f}ms")
    if save_images:
        print(f"  PyAV图片已保存到: {pyav_dir}")

    result = {
        'test_mode': 'pyav',
        'group_size': group_size,
        'total_groups': total_groups,
        'selected_groups_count': len(selected_groups),
        'frame_count': len(selected_frames),
        'time': pyav_time,
        'errors': pyav_errors,
        'avg_ms': (pyav_time/len(selected_frames))*1000 if len(selected_frames) > 0 else 0
    }

    with open('experiment4_separate_pyav_result.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    print(f"PyAV测试结果已保存到: experiment4_separate_pyav_result.json")
    return result

def run_both_tests(group_size: int, total_groups: int, save_images: bool):
    """同时运行JPG和PyAV测试"""
    print(f"\n🔄 === JPG vs PyAV 同时测试 ({group_size}帧一组, {total_groups}组) ===")

    frame_data_list = load_frame_data()
    if len(frame_data_list) == 0:
        print("没有可用的帧数据")
        return None

    selected_groups, selected_frames = select_test_frames(frame_data_list, group_size, total_groups)
    if not selected_groups:
        return None

    # 创建保存目录
    if save_images:
        jpg_dir = "separate_tests/jpg"
        pyav_dir = "separate_tests/pyav"
        os.makedirs(jpg_dir, exist_ok=True)
        os.makedirs(pyav_dir, exist_ok=True)

    # JPG读取测试
    print("\n🖼️  JPG读取测试（基准）...")
    start_time = time.time()
    jpg_results = []
    jpg_errors = 0
    for i, frame_data in enumerate(selected_frames):
        try:
            frame = read_jpg_frame(frame_data.jpg_path)
            jpg_results.append(frame.shape)

            # 保存图片
            if save_images:
                clip_name = os.path.basename(frame_data.clip_path).replace('.json', '')
                filename = f"{clip_name}_frame{frame_data.frame_index}.jpg"
                save_path = os.path.join(jpg_dir, filename)
                cv2.imwrite(save_path, frame)

            if (i + 1) % 25 == 0:
                print(f"  JPG已读取 {i + 1}/{len(selected_frames)} 帧")
        except Exception as e:
            jpg_errors += 1
            print(f"  JPG读取错误 (帧{i+1}): {e}")
    jpg_time = time.time() - start_time

    print(f"JPG读取完成: {jpg_time:.4f}秒, 成功 {len(jpg_results)} 帧, 失败 {jpg_errors} 帧")

    # PyAV读取测试
    print("\n🚀 PyAV读取测试...")
    start_time = time.time()
    pyav_results = []
    pyav_errors = 0

    # 按(视频路径, 关键帧)分组进行批量读取
    frame_groups = {}
    for i, frame_data in enumerate(selected_frames):
        group_key = (frame_data.video_path, frame_data.key_frame_index)
        if group_key not in frame_groups:
            frame_groups[group_key] = []
        frame_groups[group_key].append((i, frame_data))

    for (video_path, key_frame_index), group_items in frame_groups.items():
        # 找到最小的目标帧作为起始点
        min_target_frame = min(item[1].frame_index for item in group_items)

        try:
            # 批量读取从最小目标帧开始的连续帧
            frames_batch = read_continuous_frames_from_key_pyav(
                video_path, key_frame_index, min_target_frame, group_size
            )

            # 将读取的帧分配给对应的索引
            for idx, (result_idx, frame_data) in enumerate(group_items):
                relative_index = frame_data.frame_index - min_target_frame
                if relative_index < len(frames_batch):
                    frame = frames_batch[relative_index]
                    pyav_results.append(frame.shape)

                    # 保存PyAV读取的图片
                    if save_images:
                        clip_name = os.path.basename(frame_data.clip_path).replace('.json', '')
                        filename = f"{clip_name}_frame{frame_data.frame_index}_pyav.jpg"
                        save_path = os.path.join(pyav_dir, filename)
                        cv2.imwrite(save_path, frame)
                else:
                    pyav_results.append(None)
                    pyav_errors += 1

        except Exception as e:
            # 如果批量读取失败，记录错误
            for i, (result_idx, frame_data) in enumerate(group_items):
                pyav_results.append(None)
                pyav_errors += 1
            print(f"  PyAV批量读取失败组 {video_path}:{key_frame_index}: {e}")

    # 确保pyav_results长度正确
    while len(pyav_results) < len(selected_frames):
        pyav_results.append(None)
        pyav_errors += 1

    pyav_time = time.time() - start_time

    print(f"PyAV读取完成: {pyav_time:.4f}秒, 成功 {len(pyav_results)} 帧, 失败 {pyav_errors} 帧")

    # 结果分析
    print(f"\n📊 测试结果:")
    print(f"  关键帧组数: {len(selected_groups)}")
    print(f"  总帧数: {len(selected_frames)}")
    print(f"  JPG时间: {jpg_time:.4f}秒")
    print(f"  PyAV时间: {pyav_time:.4f}秒")
    print(f"  JPG平均每帧: {(jpg_time/len(selected_frames))*1000:.2f}ms")
    print(f"  PyAV平均每帧: {(pyav_time/len(selected_frames))*1000:.2f}ms")

    # 计算加速比
    pyav_speedup = jpg_time / pyav_time if pyav_time > 0 else 0

    print(f"\n🏆 性能对比:")
    if pyav_speedup > 1:
        print(f"  🚀 PyAV比JPG快 {pyav_speedup:.2f} 倍")
    else:
        print(f"  🐌 JPG比PyAV快 {1/pyav_speedup:.2f} 倍")

    result = {
        'test_mode': 'both',
        'group_size': group_size,
        'total_groups': total_groups,
        'selected_groups_count': len(selected_groups),
        'frame_count': len(selected_frames),
        'jpg_time': jpg_time,
        'pyav_time': pyav_time,
        'jpg_errors': jpg_errors,
        'pyav_errors': pyav_errors,
        'jpg_avg_ms': (jpg_time/len(selected_frames))*1000 if len(selected_frames) > 0 else 0,
        'pyav_avg_ms': (pyav_time/len(selected_frames))*1000 if len(selected_frames) > 0 else 0,
        'pyav_speedup': pyav_speedup
    }

    with open('experiment4_separate_both_result.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    print(f"对比测试结果已保存到: experiment4_separate_both_result.json")
    return result

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='JPG vs PyAV 单独/对比性能实验')
    parser.add_argument('--group-size', type=int, default=5, help='每组帧数')
    parser.add_argument('--total-groups', type=int, default=20, help='总组数')
    parser.add_argument('--save-images', action='store_true', help='保存对比图片到本地')
    parser.add_argument('--mode', choices=['jpg', 'pyav', 'both'], default='both',
                       help='测试模式: jpg(只测试JPG), pyav(只测试PyAV), both(测试两者)')

    args = parser.parse_args()

    result = None

    if args.mode == 'jpg':
        result = run_jpg_test(args.group_size, args.total_groups, args.save_images)
    elif args.mode == 'pyav':
        result = run_pyav_test(args.group_size, args.total_groups, args.save_images)
    elif args.mode == 'both':
        result = run_both_tests(args.group_size, args.total_groups, args.save_images)

    if result:
        print(f"\n🎉 测试完成！使用模式: {args.mode}")
        print(f"📊 结果文件: {'experiment4_separate_' + args.mode + '_result.json' if args.mode != 'both' else 'experiment4_separate_both_result.json'}")