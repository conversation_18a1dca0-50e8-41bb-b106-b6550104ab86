#!/usr/bin/env python3
import json
import os
import random
import time
import cv2
import numpy as np
from typing import List, Tuple, Dict
from collections import defaultdict

class FrameData:
    def __init__(self, clip_path: str, frame_index: int, video_path: str, jpg_path: str, key_frame_index: int):
        self.clip_path = clip_path
        self.frame_index = frame_index
        self.video_path = video_path
        self.jpg_path = jpg_path
        self.key_frame_index = key_frame_index

class OptimizedVideoReader:
    """优化的视频读取器，充分利用H.265解码特性和连续帧优化"""

    def __init__(self):
        self.video_handles = {}  # 视频文件句柄缓存

    def get_video_handle(self, video_path: str) -> cv2.VideoCapture:
        """获取视频句柄，避免重复打开"""
        if video_path not in self.video_handles:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise Exception(f"无法打开视频文件: {video_path}")
            self.video_handles[video_path] = cap
        return self.video_handles[video_path]

    def read_single_frame(self, frame_data: FrameData) -> np.ndarray:
        """读取单帧（用于实验1 - 随机帧）"""
        cap = self.get_video_handle(frame_data.video_path)

        # seek到目标帧（FFmpeg会自动找到最近I帧并解码到目标帧）
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_data.frame_index)
        ret, frame = cap.read()

        if not ret:
            raise Exception(f"无法读取帧: {frame_data.video_path} 第{frame_data.frame_index}帧")
        return frame

    def read_continuous_frames_batch(self, frame_list: List[FrameData]) -> List[np.ndarray]:
        """批量读取连续帧组（用于实验2）"""
        if not frame_list:
            return []

        results = [None] * len(frame_list)

        # 按视频文件分组
        video_groups = defaultdict(list)
        for i, frame_data in enumerate(frame_list):
            video_groups[frame_data.video_path].append((i, frame_data))

        # 对每个视频文件进行优化读取
        for video_path, frame_items in video_groups.items():
            # 按frame_index排序
            frame_items.sort(key=lambda x: x[1].frame_index)

            cap = self.get_video_handle(video_path)

            # 检测连续组 - 改进的连续性检测
            i = 0
            while i < len(frame_items):
                start_idx, start_frame = frame_items[i]

                # 找到连续的帧组
                end_idx = i
                while (end_idx + 1 < len(frame_items)):
                    current_idx, current_frame = frame_items[end_idx]
                    next_idx, next_frame = frame_items[end_idx + 1]
                    if next_frame.frame_index == current_frame.frame_index + 1:
                        end_idx += 1
                    else:
                        break

                # 批量读取这个连续组
                first_frame_data = frame_items[i][1]
                last_frame_data = frame_items[end_idx][1]

                print(f"  读取连续组: 帧{first_frame_data.frame_index}到帧{last_frame_data.frame_index}")

                # seek到组内第一帧
                cap.set(cv2.CAP_PROP_POS_FRAMES, first_frame_data.frame_index)

                # 连续读取整个组
                current_frame_index = first_frame_data.frame_index
                frame_idx_in_group = i

                while current_frame_index <= last_frame_data.frame_index and frame_idx_in_group <= end_idx:
                    ret, frame = cap.read()
                    if not ret:
                        break

                    # 找到需要这一帧的索引
                    while (frame_idx_in_group <= end_idx and
                           frame_items[frame_idx_in_group][1].frame_index == current_frame_index):
                        result_idx = frame_items[frame_idx_in_group][0]
                        results[result_idx] = frame
                        frame_idx_in_group += 1

                    current_frame_index += 1

                i = end_idx + 1

        return results

    def read_keyframe_continuous_frames_batch(self, frame_list: List[FrameData]) -> List[np.ndarray]:
        """基于关键帧的连续帧批量读取（用于实验3）"""
        if not frame_list:
            return []

        results = [None] * len(frame_list)

        # 按(视频路径, 关键帧索引)分组
        keyframe_groups = defaultdict(list)
        for i, frame_data in enumerate(frame_list):
            group_key = (frame_data.video_path, frame_data.key_frame_index)
            keyframe_groups[group_key].append((i, frame_data))

        # 对每个关键帧组进行优化读取
        for (video_path, keyframe_index), frame_items in keyframe_groups.items():
            # 按frame_index排序
            frame_items.sort(key=lambda x: x[1].frame_index)

            cap = self.get_video_handle(video_path)

            # 检测是否关键帧在请求的帧中
            keyframe_in_request = any(
                item[1].frame_index == keyframe_index
                for item in frame_items
            )

            if keyframe_in_request:
                # seek到关键帧位置（I帧）
                cap.set(cv2.CAP_PROP_POS_FRAMES, keyframe_index)

                # 从关键帧开始连续读取到最大请求帧
                max_frame_index = max(item[1].frame_index for item in frame_items)

                current_frame_index = keyframe_index
                item_idx = 0

                while current_frame_index <= max_frame_index and item_idx < len(frame_items):
                    ret, frame = cap.read()
                    if not ret:
                        break

                    # 处理所有需要当前帧的请求
                    while (item_idx < len(frame_items) and
                           frame_items[item_idx][1].frame_index == current_frame_index):
                        result_idx = frame_items[item_idx][0]
                        results[result_idx] = frame
                        item_idx += 1

                    current_frame_index += 1
            else:
                # 关键帧不在请求中，但仍需从关键帧开始读取
                # 这里按普通连续帧处理
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_items[0][1].frame_index)

                for item_idx, (result_idx, frame_data) in enumerate(frame_items):
                    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_data.frame_index)
                    ret, frame = cap.read()
                    if ret:
                        results[result_idx] = frame

        return results

    def clear_caches(self):
        """清除所有缓存"""
        for cap in self.video_handles.values():
            cap.release()
        self.video_handles.clear()

def load_frame_data():
    """加载所有帧数据"""
    print("加载clip数据...")
    frame_data_list = []

    with open('local_z10_10.json', 'r') as f:
        paths_data = json.load(f)

    clip_paths = paths_data['paths']

    for clip_path in clip_paths:
        # 读取对应的video json文件
        clip_dir = os.path.dirname(clip_path)
        clip_name = os.path.splitext(os.path.basename(clip_path))[0]
        video_json_path = os.path.join(clip_dir, f"{clip_name}_video.json")

        if not os.path.exists(video_json_path):
            print(f"警告: 找不到video json文件: {video_json_path}")
            continue

        try:
            with open(video_json_path, 'r') as f:
                video_data = json.load(f)

            video_path = os.path.join(clip_dir, f"{clip_name}.mp4")

            for frame in video_data.get('frames', []):
                sensor_data = frame.get('sensor_data', {})
                cam_data = sensor_data.get('cam_front_120', {})

                if 'video_path' in cam_data and 'file_path' in cam_data:
                    frame_data = FrameData(
                        clip_path=clip_path,
                        frame_index=cam_data.get('frame_index', 0),
                        video_path=cam_data['video_path'],
                        jpg_path=cam_data['file_path'],
                        key_frame_index=cam_data.get('key_frame_index', 0)
                    )
                    frame_data_list.append(frame_data)

        except Exception as e:
            print(f"加载clip数据失败: {e}")
            continue

    print(f"总共加载了 {len(frame_data_list)} 帧数据")
    return frame_data_list

def read_jpg_frame(jpg_path: str) -> np.ndarray:
    """从JPG文件读取图片"""
    if not os.path.exists(jpg_path):
        raise Exception(f"JPG文件不存在: {jpg_path}")
    return cv2.imread(jpg_path)

def experiment1_optimized_random_frames(frame_count: int = 100):
    """实验1优化版: 随机读取100帧（无优化空间，作为基准）"""
    print(f"\n=== 实验1优化版: 随机读取{frame_count}帧 ===")
    print("注意: 随机帧无法优化，每次都要seek+从I帧解码")

    frame_data_list = load_frame_data()
    if len(frame_data_list) == 0:
        print("没有可用的帧数据")
        return

    # 随机选择frame_count帧
    selected_frames = random.sample(frame_data_list, min(frame_count, len(frame_data_list)))
    print(f"选择了 {len(selected_frames)} 帧进行测试")

    reader = OptimizedVideoReader()

    # JPG读取测试
    print("\n🖼️  JPG读取测试...")
    start_time = time.time()
    jpg_results = []
    jpg_errors = 0
    for i, frame_data in enumerate(selected_frames):
        try:
            frame = read_jpg_frame(frame_data.jpg_path)
            jpg_results.append(frame.shape)
            if (i + 1) % 20 == 0:
                print(f"  已读取 {i + 1}/{len(selected_frames)} 帧")
        except Exception as e:
            jpg_errors += 1
    jpg_time = time.time() - start_time

    print(f"JPG读取完成: {jpg_time:.4f}秒, 成功 {len(jpg_results)} 帧, 失败 {jpg_errors} 帧")

    # 视频读取测试（优化版：使用句柄缓存）
    print("\n🎬 视频读取测试（优化版：句柄缓存）...")
    start_time = time.time()
    video_results = []
    video_errors = 0
    for i, frame_data in enumerate(selected_frames):
        try:
            frame = reader.read_single_frame(frame_data)
            video_results.append(frame.shape)
            if (i + 1) % 20 == 0:
                print(f"  已读取 {i + 1}/{len(selected_frames)} 帧")
        except Exception as e:
            video_errors += 1
    video_time = time.time() - start_time

    reader.clear_caches()

    print(f"视频读取完成: {video_time:.4f}秒, 成功 {len(video_results)} 帧, 失败 {video_errors} 帧")

    # 结果分析
    print(f"\n📊 实验1优化版结果:")
    print(f"  总帧数: {len(selected_frames)}")
    print(f"  JPG时间: {jpg_time:.4f}秒")
    print(f"  视频时间: {video_time:.4f}秒")
    print(f"  JPG平均每帧: {(jpg_time/len(selected_frames))*1000:.2f}ms")
    print(f"  视频平均每帧: {(video_time/len(selected_frames))*1000:.2f}ms")

    speedup = jpg_time / video_time if video_time > 0 else 0
    if speedup > 1:
        print(f"  🚀 视频比JPG快 {speedup:.2f} 倍")
    else:
        print(f"  🐌 JPG比视频快 {1/speedup:.2f} 倍")

    return {
        'experiment': 'experiment1_optimized',
        'frame_count': len(selected_frames),
        'jpg_time': jpg_time,
        'video_time': video_time,
        'jpg_errors': jpg_errors,
        'video_errors': video_errors,
        'jpg_avg_ms': (jpg_time/len(selected_frames))*1000,
        'video_avg_ms': (video_time/len(selected_frames))*1000,
        'speedup': speedup
    }

def experiment2_optimized_continuous_frames(group_size: int = 5, total_groups: int = 20):
    """实验2优化版: 连续帧批量读取"""
    print(f"\n=== 实验2优化版: {group_size}帧一组连续帧批量读取 ({total_groups}组) ===")
    print("优化策略: 按连续组批量读取，减少seek操作")

    frame_data_list = load_frame_data()
    if len(frame_data_list) == 0:
        print("没有可用的帧数据")
        return

    # 按clip分组
    frames_by_clip = {}
    for frame_data in frame_data_list:
        clip_key = frame_data.clip_path
        if clip_key not in frames_by_clip:
            frames_by_clip[clip_key] = []
        frames_by_clip[clip_key].append(frame_data)

    # 为每个clip按frame_index排序
    for clip_key in frames_by_clip:
        frames_by_clip[clip_key].sort(key=lambda x: x.frame_index)

    # 随机选择组和起始帧
    all_groups = []
    for clip_key, frames in frames_by_clip.items():
        if len(frames) >= group_size:
            max_start = len(frames) - group_size
            for start_pos in range(max_start + 1):
                group = frames[start_pos:start_pos + group_size]
                all_groups.append((clip_key, start_pos, group))

    # 随机选择total_groups个组
    selected_groups = random.sample(all_groups, min(total_groups, len(all_groups)))
    selected_frames = []

    for clip_key, start_pos, group in selected_groups:
        selected_frames.extend(group)

    print(f"选择了 {len(selected_groups)} 个连续帧组，共 {len(selected_frames)} 帧")
    print(f"优化潜力: 从 {len(selected_frames)} 次seek 减少到约 {len(selected_groups)} 次seek")

    reader = OptimizedVideoReader()

    # JPG读取测试
    print("\n🖼️  JPG读取测试...")
    start_time = time.time()
    jpg_results = []
    jpg_errors = 0
    for i, frame_data in enumerate(selected_frames):
        try:
            frame = read_jpg_frame(frame_data.jpg_path)
            jpg_results.append(frame.shape)
            if (i + 1) % 25 == 0:
                print(f"  已读取 {i + 1}/{len(selected_frames)} 帧")
        except Exception as e:
            jpg_errors += 1
    jpg_time = time.time() - start_time

    print(f"JPG读取完成: {jpg_time:.4f}秒, 成功 {len(jpg_results)} 帧, 失败 {jpg_errors} 帧")

    # 视频读取测试（优化版：连续帧批量读取）
    print("\n🎬 视频读取测试（优化版：连续帧批量读取）...")
    start_time = time.time()
    video_results = []
    video_errors = 0

    try:
        video_results = reader.read_continuous_frames_batch(selected_frames)
        video_errors = video_results.count(None)
        video_time = time.time() - start_time
    except Exception as e:
        print(f"批量读取失败，回退到单帧读取: {e}")
        # 回退到单帧读取
        video_results = []
        video_errors = 0
        for frame_data in selected_frames:
            try:
                frame = reader.read_single_frame(frame_data)
                video_results.append(frame)
            except Exception as e:
                video_errors += 1
        video_time = time.time() - start_time

    reader.clear_caches()

    print(f"视频读取完成: {video_time:.4f}秒, 成功 {len(video_results)-video_errors} 帧, 失败 {video_errors} 帧")

    # 结果分析
    print(f"\n📊 实验2优化版结果:")
    print(f"  组数: {len(selected_groups)}")
    print(f"  总帧数: {len(selected_frames)}")
    print(f"  JPG时间: {jpg_time:.4f}秒")
    print(f"  视频时间: {video_time:.4f}秒")
    print(f"  JPG平均每帧: {(jpg_time/len(selected_frames))*1000:.2f}ms")
    print(f"  视频平均每帧: {(video_time/len(selected_frames))*1000:.2f}ms")

    speedup = jpg_time / video_time if video_time > 0 else 0
    if speedup > 1:
        print(f"  🚀 视频比JPG快 {speedup:.2f} 倍")
        print(f"  ⚡ 相比实验1优化: 预期 {(speedup):.2f}x 提升")
    else:
        print(f"  🐌 JPG比视频快 {1/speedup:.2f} 倍")

    return {
        'experiment': 'experiment2_optimized',
        'groups_count': len(selected_groups),
        'frame_count': len(selected_frames),
        'jpg_time': jpg_time,
        'video_time': video_time,
        'jpg_errors': jpg_errors,
        'video_errors': video_errors,
        'jpg_avg_ms': (jpg_time/len(selected_frames))*1000,
        'video_avg_ms': (video_time/len(selected_frames))*1000,
        'speedup': speedup
    }

def experiment3_optimized_keyframe_continuous_frames(group_size: int = 5, total_groups: int = 20):
    """实验3优化版: 基于关键帧的连续帧批量读取"""
    print(f"\n=== 实验3优化版: 基于关键帧的{group_size}帧一组连续帧批量读取 ({total_groups}组) ===")
    print("优化策略: 利用关键帧特性，减少解码开销")

    frame_data_list = load_frame_data()
    if len(frame_data_list) == 0:
        print("没有可用的帧数据")
        return

    # 按clip分组，找出关键帧
    keyframes_by_clip = {}
    for frame_data in frame_data_list:
        clip_key = frame_data.clip_path
        if clip_key not in keyframes_by_clip:
            keyframes_by_clip[clip_key] = []

        # 如果当前帧是关键帧
        if frame_data.frame_index == frame_data.key_frame_index:
            keyframes_by_clip[clip_key].append(frame_data)

    # 为每个clip的关键帧排序
    for clip_key in keyframes_by_clip:
        keyframes_by_clip[clip_key].sort(key=lambda x: x.frame_index)

    # 从关键帧开始构建连续组
    all_groups = []
    for clip_key, keyframes in keyframes_by_clip.items():
        # 获取该clip的所有帧并排序
        clip_frames = [f for f in frame_data_list if f.clip_path == clip_key]
        clip_frames.sort(key=lambda x: x.frame_index)

        # 为每个关键帧构建连续帧组
        for keyframe in keyframes:
            # 找到关键帧在clip_frames中的位置
            keyframe_pos = None
            for i, f in enumerate(clip_frames):
                if f.frame_index == keyframe.frame_index:
                    keyframe_pos = i
                    break

            if keyframe_pos is not None:
                # 从关键帧位置开始取group_size帧
                if keyframe_pos + group_size <= len(clip_frames):
                    group = clip_frames[keyframe_pos:keyframe_pos + group_size]
                    all_groups.append((clip_key, keyframe.frame_index, group))

    print(f"找到 {len(all_groups)} 个可能的基于关键帧的连续帧组")

    # 随机选择total_groups个组
    selected_groups = random.sample(all_groups, min(total_groups, len(all_groups)))
    selected_frames = []

    for clip_key, keyframe_index, group in selected_groups:
        selected_frames.extend(group)

    print(f"选择了 {len(selected_groups)} 个基于关键帧的连续帧组，共 {len(selected_frames)} 帧")

    # 统计关键帧数量
    keyframe_count = sum(1 for f in selected_frames if f.frame_index == f.key_frame_index)
    print(f"其中关键帧: {keyframe_count} 个，非关键帧: {len(selected_frames)-keyframe_count} 个")

    reader = OptimizedVideoReader()

    # JPG读取测试
    print("\n🖼️  JPG读取测试...")
    start_time = time.time()
    jpg_results = []
    jpg_errors = 0
    for i, frame_data in enumerate(selected_frames):
        try:
            frame = read_jpg_frame(frame_data.jpg_path)
            jpg_results.append(frame.shape)
            if (i + 1) % 25 == 0:
                print(f"  已读取 {i + 1}/{len(selected_frames)} 帧")
        except Exception as e:
            jpg_errors += 1
    jpg_time = time.time() - start_time

    print(f"JPG读取完成: {jpg_time:.4f}秒, 成功 {len(jpg_results)} 帧, 失败 {jpg_errors} 帧")

    # 视频读取测试（优化版：基于关键帧的批量读取）
    print("\n🎬 视频读取测试（优化版：基于关键帧的批量读取）...")
    start_time = time.time()
    video_results = []
    video_errors = 0

    try:
        video_results = reader.read_keyframe_continuous_frames_batch(selected_frames)
        video_errors = video_results.count(None)
        video_time = time.time() - start_time
    except Exception as e:
        print(f"批量读取失败，回退到单帧读取: {e}")
        # 回退到单帧读取
        video_results = []
        video_errors = 0
        for frame_data in selected_frames:
            try:
                frame = reader.read_single_frame(frame_data)
                video_results.append(frame)
            except Exception as e:
                video_errors += 1
        video_time = time.time() - start_time

    reader.clear_caches()

    print(f"视频读取完成: {video_time:.4f}秒, 成功 {len(video_results)-video_errors} 帧, 失败 {video_errors} 帧")

    # 结果分析
    print(f"\n📊 实验3优化版结果:")
    print(f"  关键帧组数: {len(selected_groups)}")
    print(f"  总帧数: {len(selected_frames)}")
    print(f"  关键帧数: {keyframe_count}")
    print(f"  JPG时间: {jpg_time:.4f}秒")
    print(f"  视频时间: {video_time:.4f}秒")
    print(f"  JPG平均每帧: {(jpg_time/len(selected_frames))*1000:.2f}ms")
    print(f"  视频平均每帧: {(video_time/len(selected_frames))*1000:.2f}ms")

    speedup = jpg_time / video_time if video_time > 0 else 0
    if speedup > 1:
        print(f"  🚀 视频比JPG快 {speedup:.2f} 倍")
        print(f"  ⚡ 相比实验1优化: 预期 {(speedup):.2f}x 提升")
    else:
        print(f"  🐌 JPG比视频快 {1/speedup:.2f} 倍")

    return {
        'experiment': 'experiment3_optimized',
        'keyframe_groups_count': len(selected_groups),
        'frame_count': len(selected_frames),
        'keyframe_count': keyframe_count,
        'jpg_time': jpg_time,
        'video_time': video_time,
        'jpg_errors': jpg_errors,
        'video_errors': video_errors,
        'jpg_avg_ms': (jpg_time/len(selected_frames))*1000,
        'video_avg_ms': (video_time/len(selected_frames))*1000,
        'speedup': speedup
    }

def main():
    import sys

    # 解析命令行参数
    if len(sys.argv) > 1:
        experiment_choice = sys.argv[1]
    else:
        experiment_choice = input("请选择要执行的实验 (1/2/3/all): ").strip()

    print("优化版读取性能测试")
    print("=" * 80)
    print("优化策略:")
    print("1. 实验1: 随机帧 - 使用句柄缓存（基准）")
    print("2. 实验2: 连续帧 - 批量读取，减少seek操作")
    print("3. 实验3: 关键帧组 - 利用I帧解码特性")
    print("all: 运行所有实验")
    print("=" * 80)

    results = []

    if experiment_choice == "1":
        print("🔬 执行实验1: 随机帧读取")
        results.append(experiment1_optimized_random_frames(100))

    elif experiment_choice == "2":
        print("🔬 执行实验2: 连续帧批量读取")
        results.append(experiment2_optimized_continuous_frames(5, 20))

    elif experiment_choice == "3":
        print("🔬 执行实验3: 基于关键帧的连续帧读取")
        results.append(experiment3_optimized_keyframe_continuous_frames(5, 20))

    elif experiment_choice.lower() == "all":
        print("🔬 执行所有实验")
        results.append(experiment1_optimized_random_frames(100))
        print("\n⏳ 等待5秒让系统缓存清理...")
        time.sleep(5)

        results.append(experiment2_optimized_continuous_frames(5, 20))
        print("\n⏳ 等待5秒让系统缓存清理...")
        time.sleep(5)

        results.append(experiment3_optimized_keyframe_continuous_frames(5, 20))

    else:
        print("❌ 无效选择，请输入 1, 2, 3, 或 all")
        print("\n📖 使用说明:")
        print("  python3 optimized_experiments.py 1     # 只执行实验1")
        print("  python3 optimized_experiments.py 2     # 只执行实验2")
        print("  python3 optimized_experiments.py 3     # 只执行实验3")
        print("  python3 optimized_experiments.py all   # 执行所有实验")
        print("  python3 optimized_experiments.py       # 交互式选择")
        return

    if results:
        # 保存结果
        if len(results) == 1:
            filename = f"experiment{experiment_choice}_optimized_result.json"
        else:
            filename = "optimized_experiments_result.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"\n📄 实验结果已保存到: {filename}")

        # 打印结果
        if len(results) == 1:
            result = results[0]
            print(f"\n📊 单实验结果:")
            print(f"  加速比: {result['speedup']:.2f}x")
            print(f"  视频平均: {result['video_avg_ms']:.2f}ms/帧")
        else:
            print(f"\n" + "="*80)
            print("优化版综合对比")
            print("="*80)

            for i, result in enumerate(results, 1):
                print(f"\n实验{i}: {result['experiment']}")
                print(f"  加速比: {result['speedup']:.2f}x")
                print(f"  视频平均: {result['video_avg_ms']:.2f}ms/帧")

if __name__ == "__main__":
    main()